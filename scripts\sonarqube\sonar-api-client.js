#!/usr/bin/env node

/**
 * Enhanced SonarQube API Client for Healtether Projects
 * Fetches comprehensive analysis data from SonarQube API with improved error handling
 * 
 * Usage:
 *   node scripts/sonarqube/sonar-api-client.js [project-key]
 *   node scripts/sonarqube/sonar-api-client.js healtether-communications-api
 *   node scripts/sonarqube/sonar-api-client.js healtether-clinics-api
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const SONARQUBE_CONFIG = {
    baseURL: 'http://localhost:9000',
    // Authentication tokens from memories
    tokens: {
        'healtether-communications-api': 'sqp_0e576c502636aec9dd0b2e1ebba6f68ce1a948f6',
        'healtether-clinics-api': 'sqp_9833dac32acc6233060116e797a16d7c314fcf5c'
    },
    projects: {
        'healtether-communications-api': 'Healtether Communications API',
        'healtether-clinics-api': 'Healtether Clinics API'
    }
};

class SonarQubeAPIClient {
    constructor(projectKey) {
        this.projectKey = projectKey;
        this.token = SONARQUBE_CONFIG.tokens[projectKey];
        this.projectName = SONARQUBE_CONFIG.projects[projectKey];
        
        if (!this.token) {
            throw new Error(`No authentication token found for project: ${projectKey}`);
        }

        this.client = axios.create({
            baseURL: SONARQUBE_CONFIG.baseURL,
            auth: {
                username: this.token,
                password: ''
            },
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000  // 30 second timeout
        });
    }

    async fetchProjectStatus() {
        try {
            console.log('📊 Fetching project status...');
            const response = await this.client.get(`/api/qualitygates/project_status`, {
                params: { projectKey: this.projectKey }
            });
            return response.data;
        } catch (error) {
            console.error('❌ Error fetching project status:', error.message);
            return null;
        }
    }

    async fetchMeasures() {
        try {
            console.log('📈 Fetching project measures...');
            const metrics = [
                'alert_status', 'quality_gate_details',
                'bugs', 'vulnerabilities', 'security_hotspots', 'code_smells',
                'coverage', 'duplicated_lines_density',
                'ncloc', 'complexity', 'cognitive_complexity',
                'reliability_rating', 'security_rating', 'sqale_rating',
                'sqale_index', 'sqale_debt_ratio',
                'new_bugs', 'new_vulnerabilities', 'new_security_hotspots', 'new_code_smells',
                'new_coverage', 'new_duplicated_lines_density',
                'new_lines', 'new_reliability_rating', 'new_security_rating', 'new_maintainability_rating'
            ];

            const response = await this.client.get(`/api/measures/component`, {
                params: {
                    component: this.projectKey,
                    metricKeys: metrics.join(',')
                }
            });
            return response.data;
        } catch (error) {
            console.error('❌ Error fetching measures:', error.message);
            return null;
        }
    }

    async fetchIssues(type = 'all', pageSize = 500) {
        try {
            console.log(`🐛 Fetching ${type} issues...`);
            const params = {
                componentKeys: this.projectKey,
                ps: pageSize,
                additionalFields: 'rules'
            };

            if (type !== 'all') {
                params.types = type.toUpperCase();
            }

            const response = await this.client.get(`/api/issues/search`, { params });
            console.log(`✅ Successfully fetched ${response.data.total} issues`);
            return response.data;
        } catch (error) {
            console.error(`❌ Error fetching ${type} issues:`, error.response?.status, error.response?.data || error.message);
            return null;
        }
    }

    async fetchSecurityHotspots() {
        try {
            console.log('🔥 Fetching security hotspots...');
            const params = {
                projectKey: this.projectKey,
                ps: 500
            };

            const response = await this.client.get(`/api/hotspots/search`, { params });
            console.log(`✅ Successfully fetched ${response.data.hotspots?.length || 0} security hotspots`);
            return response.data;
        } catch (error) {
            console.error('❌ Error fetching security hotspots:', error.response?.status, error.response?.data || error.message);
            return null;
        }
    }

    formatMetric(metric) {
        const value = metric.value;
        const metricKey = metric.metric;

        // Format different types of metrics
        switch (metricKey) {
            case 'coverage':
            case 'new_coverage':
            case 'duplicated_lines_density':
            case 'new_duplicated_lines_density':
            case 'sqale_debt_ratio':
                return `${value}%`;
            
            case 'sqale_index':
                const hours = Math.floor(value / 60);
                const minutes = value % 60;
                return `${hours}h ${minutes}m`;
            
            case 'reliability_rating':
            case 'security_rating':
            case 'sqale_rating':
            case 'new_reliability_rating':
            case 'new_security_rating':
            case 'new_maintainability_rating':
                const ratings = ['A', 'B', 'C', 'D', 'E'];
                return ratings[parseInt(value) - 1] || value;
            
            default:
                return value;
        }
    }

    generateReport(data) {
        const timestamp = new Date().toISOString();
        const report = {
            project: {
                key: this.projectKey,
                name: this.projectName,
                analysisTime: timestamp
            },
            qualityGate: data.projectStatus,
            metrics: {},
            issues: {
                summary: {},
                details: data.issues
            },
            securityHotspots: data.securityHotspots
        };

        // Process measures
        if (data.measures && data.measures.component && data.measures.component.measures) {
            data.measures.component.measures.forEach(measure => {
                report.metrics[measure.metric] = {
                    value: measure.value,
                    formatted: this.formatMetric(measure)
                };
            });
        }

        // Process issues summary
        if (data.issues) {
            report.issues.summary = {
                total: data.issues.total,
                bugs: data.issues.issues.filter(i => i.type === 'BUG').length,
                vulnerabilities: data.issues.issues.filter(i => i.type === 'VULNERABILITY').length,
                codeSmells: data.issues.issues.filter(i => i.type === 'CODE_SMELL').length,
                severities: {
                    blocker: data.issues.issues.filter(i => i.severity === 'BLOCKER').length,
                    critical: data.issues.issues.filter(i => i.severity === 'CRITICAL').length,
                    major: data.issues.issues.filter(i => i.severity === 'MAJOR').length,
                    minor: data.issues.issues.filter(i => i.severity === 'MINOR').length,
                    info: data.issues.issues.filter(i => i.severity === 'INFO').length
                }
            };
        }

        return report;
    }

    async generateFullReport() {
        console.log(`🔍 Fetching SonarQube analysis data for: ${this.projectName}`);
        console.log(`📊 Project Key: ${this.projectKey}`);
        console.log('━'.repeat(80));

        const data = {};

        // Fetch all data in parallel
        const [projectStatus, measures, issues, securityHotspots] = await Promise.all([
            this.fetchProjectStatus(),
            this.fetchMeasures(),
            this.fetchIssues(),
            this.fetchSecurityHotspots()
        ]);

        data.projectStatus = projectStatus;
        data.measures = measures;
        data.issues = issues;
        data.securityHotspots = securityHotspots;

        const report = this.generateReport(data);

        // Ensure sonar-reports directory exists (relative to project root)
        const reportsDir = path.resolve(process.cwd(), 'sonar-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
        
        // Save detailed report to file in sonar-reports folder
        const reportFile = path.join(reportsDir, `sonarqube-report-${this.projectKey}.json`);
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

        console.log(`📄 Detailed report saved to: ${reportFile}`);
        
        return report;
    }

    printSummary(report) {
        console.log('\n🎯 QUALITY GATE STATUS');
        console.log('━'.repeat(50));
        if (report.qualityGate && report.qualityGate.projectStatus) {
            const status = report.qualityGate.projectStatus.status;
            const emoji = status === 'OK' ? '✅' : '❌';
            console.log(`${emoji} Status: ${status}`);
            
            if (report.qualityGate.projectStatus.conditions) {
                report.qualityGate.projectStatus.conditions.forEach(condition => {
                    const conditionEmoji = condition.status === 'OK' ? '✅' : '❌';
                    console.log(`  ${conditionEmoji} ${condition.metricKey}: ${condition.actualValue} (threshold: ${condition.errorThreshold})`);
                });
            }
        }

        console.log('\n📊 KEY METRICS');
        console.log('━'.repeat(50));
        const keyMetrics = [
            'bugs', 'vulnerabilities', 'security_hotspots', 'code_smells',
            'coverage', 'duplicated_lines_density', 'ncloc',
            'reliability_rating', 'security_rating', 'sqale_rating'
        ];

        keyMetrics.forEach(metric => {
            if (report.metrics[metric]) {
                console.log(`${metric.padEnd(25)}: ${report.metrics[metric].formatted}`);
            }
        });

        console.log('\n🐛 ISSUES SUMMARY');
        console.log('━'.repeat(50));
        if (report.issues.summary) {
            const summary = report.issues.summary;
            console.log(`Total Issues: ${summary.total}`);
            console.log(`  🐛 Bugs: ${summary.bugs}`);
            console.log(`  🔒 Vulnerabilities: ${summary.vulnerabilities}`);
            console.log(`  💨 Code Smells: ${summary.codeSmells}`);
            
            console.log('\nBy Severity:');
            Object.entries(summary.severities).forEach(([severity, count]) => {
                if (count > 0) {
                    console.log(`  ${severity.toUpperCase()}: ${count}`);
                }
            });
        }

        if (report.securityHotspots && report.securityHotspots.hotspots) {
            console.log(`\n🔥 Security Hotspots: ${report.securityHotspots.hotspots.length}`);
        }
    }
}

// Main execution
async function main() {
    const projectKey = process.argv[2] || 'healtether-communications-api';
    
    if (!SONARQUBE_CONFIG.projects[projectKey]) {
        console.error(`❌ Unknown project key: ${projectKey}`);
        console.log('Available projects:');
        Object.keys(SONARQUBE_CONFIG.projects).forEach(key => {
            console.log(`  - ${key}`);
        });
        process.exit(1);
    }

    try {
        const client = new SonarQubeAPIClient(projectKey);
        const report = await client.generateFullReport();
        client.printSummary(report);

        console.log('\n✅ Analysis complete!');
        console.log(`📊 View detailed data in the generated JSON report file`);
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = SonarQubeAPIClient;
