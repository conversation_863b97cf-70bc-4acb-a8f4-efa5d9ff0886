{"@programName": "ZAP", "@version": "2.16.1", "@generated": "Thu, 19 Jun 2025 09:05:03", "site": [{"@name": "http://host.docker.internal:2222", "@host": "host.docker.internal", "@port": "2222", "@ssl": "false", "alerts": [{"pluginid": "40040", "alertRef": "40040", "alert": "CORS Header", "name": "CORS Header", "riskcode": "0", "confidence": "3", "riskdesc": "Informational (High)", "desc": "<p>Cross-Origin Resource Sharing (CORS) is an HTTP-header based mechanism that allows a server to indicate any other origins (domain, scheme, or port) than its own from which a browser should permit loading of resources. It relaxes the Same-Origin Policy (SOP).</p>", "instances": [{"id": "30", "uri": "http://host.docker.internal:2222", "method": "GET", "param": "", "attack": "origin: http://FBzRFAN7.com", "evidence": "", "otherinfo": ""}, {"id": "31", "uri": "http://host.docker.internal:2222/", "method": "GET", "param": "", "attack": "origin: http://FBzRFAN7.com", "evidence": "", "otherinfo": ""}, {"id": "29", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "", "attack": "origin: http://FBzRFAN7.com", "evidence": "", "otherinfo": ""}, {"id": "32", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "", "attack": "origin: http://FBzRFAN7.com", "evidence": "", "otherinfo": ""}], "count": "4", "solution": "<p>If a web resource contains sensitive information, the origin should be properly specified in the Access-Control-Allow-Origin header. Only trusted websites needing this resource should be specified in this header, with the most secured protocol supported.</p>", "otherinfo": "", "reference": "<p>https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS</p><p>https://portswigger.net/web-security/cors</p>", "cweid": "942", "wascid": "14", "sourceid": "247"}, {"pluginid": "10049", "alertRef": "10049", "alert": "Non-Storable Content", "name": "Non-Storable Content", "riskcode": "0", "confidence": "2", "riskdesc": "Informational (Medium)", "desc": "<p>The response contents are not storable by caching components such as proxy servers. If the response does not contain sensitive, personal or user-specific information, it may benefit from being stored and cached, to improve performance.</p>", "instances": [{"id": "3", "uri": "http://host.docker.internal:2222", "method": "GET", "param": "", "attack": "", "evidence": "no-store", "otherinfo": ""}, {"id": "0", "uri": "http://host.docker.internal:2222/", "method": "GET", "param": "", "attack": "", "evidence": "no-store", "otherinfo": ""}, {"id": "1", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "", "attack": "", "evidence": "no-store", "otherinfo": ""}, {"id": "2", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "", "attack": "", "evidence": "no-store", "otherinfo": ""}], "count": "4", "solution": "<p>The content may be marked as storable by ensuring that the following conditions are satisfied:</p><p>The request method must be understood by the cache and defined as being cacheable (\"GET\", \"HEAD\", and \"POST\" are currently defined as cacheable)</p><p>The response status code must be understood by the cache (one of the 1XX, 2XX, 3XX, 4XX, or 5XX response classes are generally understood)</p><p>The \"no-store\" cache directive must not appear in the request or response header fields</p><p>For caching by \"shared\" caches such as \"proxy\" caches, the \"private\" response directive must not appear in the response</p><p>For caching by \"shared\" caches such as \"proxy\" caches, the \"Authorization\" header field must not appear in the request, unless the response explicitly allows it (using one of the \"must-revalidate\", \"public\", or \"s-maxage\" Cache-Control response directives)</p><p>In addition to the conditions above, at least one of the following conditions must also be satisfied by the response:</p><p>It must contain an \"Expires\" header field</p><p>It must contain a \"max-age\" response directive</p><p>For \"shared\" caches such as \"proxy\" caches, it must contain a \"s-maxage\" response directive</p><p>It must contain a \"Cache Control Extension\" that allows it to be cached</p><p>It must have a status code that is defined as cacheable by default (200, 203, 204, 206, 300, 301, 404, 405, 410, 414, 501).</p>", "otherinfo": "", "reference": "<p>https://datatracker.ietf.org/doc/html/rfc7234</p><p>https://datatracker.ietf.org/doc/html/rfc7231</p><p>https://www.w3.org/Protocols/rfc2616/rfc2616-sec13.html</p>", "cweid": "524", "wascid": "13", "sourceid": "10"}, {"pluginid": "10104", "alertRef": "10104", "alert": "User Agent Fuzzer", "name": "User Agent Fuzzer", "riskcode": "0", "confidence": "2", "riskdesc": "Informational (Medium)", "desc": "<p>Check for differences in response based on fuzzed User Agent (eg. mobile sites, access as a Search Engine Crawler). Compares the response statuscode and the hashcode of the response body with the original response.</p>", "instances": [{"id": "10", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)", "evidence": "", "otherinfo": ""}, {"id": "8", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)", "evidence": "", "otherinfo": ""}, {"id": "5", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)", "evidence": "", "otherinfo": ""}, {"id": "12", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko", "evidence": "", "otherinfo": ""}, {"id": "13", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3739.0 Safari/537.36 Edg/**********", "evidence": "", "otherinfo": ""}, {"id": "28", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "evidence": "", "otherinfo": ""}, {"id": "26", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/91.0", "evidence": "", "otherinfo": ""}, {"id": "15", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "evidence": "", "otherinfo": ""}, {"id": "20", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)", "evidence": "", "otherinfo": ""}, {"id": "24", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A366 Safari/600.1.4", "evidence": "", "otherinfo": ""}, {"id": "22", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16", "evidence": "", "otherinfo": ""}, {"id": "17", "uri": "http://host.docker.internal:2222/robots.txt", "method": "GET", "param": "Header User-Agent", "attack": "msnbot/1.1 (+http://search.msn.com/msnbot.htm)", "evidence": "", "otherinfo": ""}, {"id": "9", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)", "evidence": "", "otherinfo": ""}, {"id": "7", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)", "evidence": "", "otherinfo": ""}, {"id": "6", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)", "evidence": "", "otherinfo": ""}, {"id": "11", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko", "evidence": "", "otherinfo": ""}, {"id": "14", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3739.0 Safari/537.36 Edg/**********", "evidence": "", "otherinfo": ""}, {"id": "27", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "evidence": "", "otherinfo": ""}, {"id": "25", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/91.0", "evidence": "", "otherinfo": ""}, {"id": "16", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "evidence": "", "otherinfo": ""}, {"id": "19", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)", "evidence": "", "otherinfo": ""}, {"id": "23", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A366 Safari/600.1.4", "evidence": "", "otherinfo": ""}, {"id": "21", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16", "evidence": "", "otherinfo": ""}, {"id": "18", "uri": "http://host.docker.internal:2222/sitemap.xml", "method": "GET", "param": "Header User-Agent", "attack": "msnbot/1.1 (+http://search.msn.com/msnbot.htm)", "evidence": "", "otherinfo": ""}], "count": "24", "solution": "", "otherinfo": "", "reference": "<p>https://owasp.org/wstg</p>", "cweid": "0", "wascid": "0", "sourceid": "140"}]}], "sequences": []}