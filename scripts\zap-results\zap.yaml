env:
  contexts:
  - excludePaths: []
    name: baseline
    urls:
    - http://host.docker.internal:3001
  parameters:
    failOnError: true
    progressToStdout: false
jobs:
- parameters:
    enableTags: false
    maxAlertsPerRule: 10
  type: passiveScan-config
- parameters:
    maxDuration: 1
    url: http://host.docker.internal:3001
  type: spider
- parameters:
    maxDuration: 0
  type: passiveScan-wait
- parameters:
    format: Long
    summaryFile: /home/<USER>/zap_out.json
  rules: []
  type: outputSummary
- parameters:
    reportDescription: ''
    reportDir: /zap/wrk/
    reportFile: communications-baseline.html
    reportTitle: ZAP Scanning Report
    template: traditional-html
  type: report
- parameters:
    reportDescription: ''
    reportDir: /zap/wrk/
    reportFile: communications-baseline.xml
    reportTitle: ZAP Scanning Report
    template: traditional-xml
  type: report
- parameters:
    reportDescription: ''
    reportDir: /zap/wrk/
    reportFile: communications-baseline.json
    reportTitle: ZAP Scanning Report
    template: traditional-json
  type: report
