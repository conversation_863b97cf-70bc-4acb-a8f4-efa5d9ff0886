import crypto from "crypto";
import fs from "fs";
import path from "path";


// Helper function to validate webhook message structure
function isValidWebhookMessage(msg) {
    return msg?.entry?.[0]?.changes?.[0]?.value?.messages?.length > 0;
}

// Helper function to extract common webhook data
function extractWebhookData(msg) {
    const value = msg.entry[0].changes[0].value;
    return {
        webhookMessage: value.messages[0],
        phoneNumberId: value.metadata.phone_number_id,
        displayName: value.contacts?.[0]?.profile?.name,
        from: value.messages[0].from
    };
}

// Helper function to handle text messages
function handleTextMessage(webhookMessage, commonData) {
    return {
        mobile: commonData.from,
        message: webhookMessage.text.body,
        phoneNumberId: commonData.phoneNumberId,
        type: "text",
        displayName: commonData.displayName
    };
}

// Helper function to handle button replies
function handleButtonReply(webhookMessage, commonData, replyMessageId) {
    return {
        mobile: commonData.from,
        phoneNumberId: commonData.phoneNumberId,
        type: "reply",
        displayName: commonData.displayName,
        messageId: replyMessageId,
        replyText: webhookMessage.interactive.button_reply.id
    };
}

// Helper function to find item by ID in array
function findItemById(array, id) {
    return array.find(element => element.id == id) || null;
}

// Helper function to handle NFM (Native Flow Message) replies
function handleNfmReply(webhookMessage, commonData, replyMessageId, interactiveType) {
    const responseJson = webhookMessage.interactive[interactiveType]["response_json"];
    const replyJson = JSON.parse(responseJson);

    if (replyJson["flow_token"] !== process.env.WHATSAPP_SCHEDULE_APPOINTMENT_FLOWTOKEN) {
        return null;
    }

    const selectedDoctor = findItemById(replyJson.data_doctors, replyJson.doctor);
    const selectedPatient = findItemById(replyJson.data_patient, replyJson.patient);
    const scheduleDate = new Date(parseInt(replyJson.scheduleDate));

    return {
        mobile: commonData.from,
        phoneNumberId: commonData.phoneNumberId,
        type: "ScheduleReply",
        displayName: commonData.displayName,
        messageId: replyMessageId,
        reply: {
            doctor: selectedDoctor,
            patient: selectedPatient,
            timeslot: replyJson.scheduleTime,
            scheduleDate: scheduleDate,
            isOnline: replyJson.isOnline
        }
    };
}

// Helper function to handle interactive messages
function handleInteractiveMessage(webhookMessage, commonData) {
    const interactiveType = webhookMessage.interactive.type;
    const replyMessageId = webhookMessage.context?.id || null;

    if (!replyMessageId) {
        return null;
    }

    switch (interactiveType) {
        case "button_reply":
            return handleButtonReply(webhookMessage, commonData, replyMessageId);
        case "nfm_reply":
            return handleNfmReply(webhookMessage, commonData, replyMessageId, interactiveType);
        default:
            return null;
    }
}

// Helper function to handle button messages
function handleButtonMessage(webhookMessage, commonData) {
    const replyMessageId = webhookMessage.context?.id;

    if (!replyMessageId) {
        return null;
    }

    return {
        mobile: commonData.from,
        phoneNumberId: commonData.phoneNumberId,
        type: "reply",
        displayName: commonData.displayName,
        messageId: replyMessageId,
        replyText: webhookMessage.button.payload
    };
}

// Main function with reduced cognitive complexity
export function getContentFromWebhookMessage(msg) {
    if (!isValidWebhookMessage(msg)) {
        return null;
    }

    const commonData = extractWebhookData(msg);
    const { webhookMessage } = commonData;

    switch (webhookMessage.type) {
        case "text":
            return handleTextMessage(webhookMessage, commonData);
        case "interactive":
            return handleInteractiveMessage(webhookMessage, commonData);
        case "button":
            return handleButtonMessage(webhookMessage, commonData);
        case "reaction":
        case "image":
        case "sticker":
        case "unknown":
        default:
            return null;
    }
}


export function decryptWhatsappRequest(body){
    const { encrypted_aes_key, encrypted_flow_data, initial_vector } = body;

    // Decrypt the AES key created by the client
    const privateKeyPath = path.join(process.cwd(), 'keys/whatsapp/private.pem');
    const privateKey = crypto.createPrivateKey({
        key: fs.readFileSync(privateKeyPath,'utf8'),
        format: 'pem',  // Ensure the format is correct
        passphrase: 'H#e@ltet#her@'  // Include if the key is encrypted
      });

    const decryptedAesKey = crypto.privateDecrypt(
      {
        key: privateKey,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: "sha256",
      },
      Buffer.from(encrypted_aes_key, "base64"),
    );
   
    // Decrypt the Flow data
    const flowDataBuffer = Buffer.from(encrypted_flow_data, "base64");
    const initialVectorBuffer = Buffer.from(initial_vector, "base64");
  
    const TAG_LENGTH = 16;
    const encrypted_flow_data_body = flowDataBuffer.subarray(0, -TAG_LENGTH);
    const encrypted_flow_data_tag = flowDataBuffer.subarray(-TAG_LENGTH);
  
    const decipher = crypto.createDecipheriv(
      "aes-128-gcm",
      decryptedAesKey,
      initialVectorBuffer,
    );
    decipher.setAuthTag(encrypted_flow_data_tag);
  
    const decryptedJSONString = Buffer.concat([
      decipher.update(encrypted_flow_data_body),
      decipher.final(),
    ]).toString("utf-8");
  
    return {
      decryptedBody: JSON.parse(decryptedJSONString),
      aesKeyBuffer: decryptedAesKey,
      initialVectorBuffer,
    };

}

export function encryptResponse  (
    response,
    aesKeyBuffer,
    initialVectorBuffer
  )
   {
    // Flip the initialization vector
    const flipped_iv = [];
    for (const pair of initialVectorBuffer.entries()) {
      flipped_iv.push(~pair[1]);
    }
    // Encrypt the response data
    const cipher = crypto.createCipheriv(
      "aes-128-gcm",
      aesKeyBuffer,
      Buffer.from(flipped_iv),
    );
    return Buffer.concat([
      cipher.update(JSON.stringify(response), "utf-8"),
      cipher.final(),
      cipher.getAuthTag(),
    ]).toString("base64");
  }

  export function checkItIsPing(msg){

    return msg.action==="ping"

  }