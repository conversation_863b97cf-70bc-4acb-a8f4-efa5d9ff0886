<?xml version="1.0"?>
<OWASPZAPReport programName="ZAP" version="2.16.1" generated="Thu, 19 Jun 2025 09:05:57">
	
		<site name="http://host.docker.internal:3001" host="host.docker.internal" port="3001" ssl="false">
			<alerts>
				
					<alertitem>
						<pluginid>10049</pluginid>
						<alertRef>10049</alertRef>
						<alert>Non-Storable Content</alert>
						<name>Non-Storable Content</name>
						<riskcode>0</riskcode>
						<confidence>2</confidence>
						<riskdesc>Informational (Medium)</riskdesc>
						<confidencedesc>Medium</confidencedesc>
						<desc>&lt;p&gt;The response contents are not storable by caching components such as proxy servers. If the response does not contain sensitive, personal or user-specific information, it may benefit from being stored and cached, to improve performance.&lt;/p&gt;</desc>
						<instances>
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param></param>
									<attack></attack>
									<evidence>no-store</evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param></param>
									<attack></attack>
									<evidence>no-store</evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param></param>
									<attack></attack>
									<evidence>no-store</evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param></param>
									<attack></attack>
									<evidence>no-store</evidence>
									<otherinfo></otherinfo>
								</instance>
							
						</instances>
						<count>4</count>
						<solution>&lt;p&gt;The content may be marked as storable by ensuring that the following conditions are satisfied:&lt;/p&gt;&lt;p&gt;The request method must be understood by the cache and defined as being cacheable (&quot;GET&quot;, &quot;HEAD&quot;, and &quot;POST&quot; are currently defined as cacheable)&lt;/p&gt;&lt;p&gt;The response status code must be understood by the cache (one of the 1XX, 2XX, 3XX, 4XX, or 5XX response classes are generally understood)&lt;/p&gt;&lt;p&gt;The &quot;no-store&quot; cache directive must not appear in the request or response header fields&lt;/p&gt;&lt;p&gt;For caching by &quot;shared&quot; caches such as &quot;proxy&quot; caches, the &quot;private&quot; response directive must not appear in the response&lt;/p&gt;&lt;p&gt;For caching by &quot;shared&quot; caches such as &quot;proxy&quot; caches, the &quot;Authorization&quot; header field must not appear in the request, unless the response explicitly allows it (using one of the &quot;must-revalidate&quot;, &quot;public&quot;, or &quot;s-maxage&quot; Cache-Control response directives)&lt;/p&gt;&lt;p&gt;In addition to the conditions above, at least one of the following conditions must also be satisfied by the response:&lt;/p&gt;&lt;p&gt;It must contain an &quot;Expires&quot; header field&lt;/p&gt;&lt;p&gt;It must contain a &quot;max-age&quot; response directive&lt;/p&gt;&lt;p&gt;For &quot;shared&quot; caches such as &quot;proxy&quot; caches, it must contain a &quot;s-maxage&quot; response directive&lt;/p&gt;&lt;p&gt;It must contain a &quot;Cache Control Extension&quot; that allows it to be cached&lt;/p&gt;&lt;p&gt;It must have a status code that is defined as cacheable by default (200, 203, 204, 206, 300, 301, 404, 405, 410, 414, 501).&lt;/p&gt;</solution>
						<otherinfo></otherinfo>
						<reference>&lt;p&gt;https://datatracker.ietf.org/doc/html/rfc7234&lt;/p&gt;&lt;p&gt;https://datatracker.ietf.org/doc/html/rfc7231&lt;/p&gt;&lt;p&gt;https://www.w3.org/Protocols/rfc2616/rfc2616-sec13.html&lt;/p&gt;</reference>
						<cweid>524</cweid>
						<wascid>13</wascid>
						<sourceid>10</sourceid>
					</alertitem>
				
				
					<alertitem>
						<pluginid>10104</pluginid>
						<alertRef>10104</alertRef>
						<alert>User Agent Fuzzer</alert>
						<name>User Agent Fuzzer</name>
						<riskcode>0</riskcode>
						<confidence>2</confidence>
						<riskdesc>Informational (Medium)</riskdesc>
						<confidencedesc>Medium</confidencedesc>
						<desc>&lt;p&gt;Check for differences in response based on fuzzed User Agent (eg. mobile sites, access as a Search Engine Crawler). Compares the response statuscode and the hashcode of the response body with the original response.&lt;/p&gt;</desc>
						<instances>
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3739.0 Safari/537.36 Edg/**********</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/91.0</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A366 Safari/600.1.4</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>msnbot/1.1 (+http://search.msn.com/msnbot.htm)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3739.0 Safari/537.36 Edg/**********</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/91.0</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A366 Safari/600.1.4</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>msnbot/1.1 (+http://search.msn.com/msnbot.htm)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3739.0 Safari/537.36 Edg/**********</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/91.0</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A366 Safari/600.1.4</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/robots.txt</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>msnbot/1.1 (+http://search.msn.com/msnbot.htm)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3739.0 Safari/537.36 Edg/**********</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/91.0</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12A366 Safari/600.1.4</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
							
								<instance>
									<uri>http://host.docker.internal:3001/sitemap.xml</uri>
									<method>GET</method>
									<param>Header User-Agent</param>
									<attack>msnbot/1.1 (+http://search.msn.com/msnbot.htm)</attack>
									<evidence></evidence>
									<otherinfo></otherinfo>
								</instance>
							
						</instances>
						<count>48</count>
						<solution></solution>
						<otherinfo></otherinfo>
						<reference>&lt;p&gt;https://owasp.org/wstg&lt;/p&gt;</reference>
						<cweid>0</cweid>
						<wascid>0</wascid>
						<sourceid>140</sourceid>
					</alertitem>
				
			</alerts>
		</site>
	
</OWASPZAPReport>