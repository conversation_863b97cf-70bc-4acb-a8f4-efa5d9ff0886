# Healtether Quality & Security Scripts

This directory contains organized scripts for code quality analysis and security testing across both Healtether projects.

## 📁 Directory Structure

```
scripts/
├── README.md                    # This file
├── sonarqube/                   # SonarQube analysis scripts
│   ├── run-sonar-clinics.ps1    # SonarQube scan for Clinics API
│   ├── run-sonar-communications.ps1 # SonarQube scan for Communications API
│   ├── run-sonar-both.ps1       # Run SonarQube for both projects
│   └── sonar-api-client.js      # Enhanced API client for detailed reports
├── security/                    # Security testing scripts
│   ├── run-zap-clinics.ps1      # ZAP security scan for Clinics API
│   ├── run-zap-communications.ps1 # ZAP security scan for Communications API
│   ├── run-zap-both.ps1         # Run ZAP scans for both projects
│   └── zap-docker-setup.ps1     # ZAP Docker environment setup
└── combined/                    # Combined analysis scripts
    ├── full-analysis-clinics.ps1    # Complete analysis for Clinics
    ├── full-analysis-communications.ps1 # Complete analysis for Communications
    └── full-analysis-both.ps1      # Complete analysis for both projects
```

## 🚀 Quick Start

### Prerequisites
- **Java 11+** (for SonarQube Scanner)
- **Node.js** (for API client)
- **Docker Desktop** (for ZAP security testing)
- **PowerShell** (Windows) or **PowerShell Core** (cross-platform)

### SonarQube Analysis
```powershell
# Analyze specific project
.\scripts\sonarqube\run-sonar-clinics.ps1
.\scripts\sonarqube\run-sonar-communications.ps1

# Analyze both projects
.\scripts\sonarqube\run-sonar-both.ps1
```

### Security Testing
```powershell
# Security scan specific project
.\scripts\security\run-zap-clinics.ps1
.\scripts\security\run-zap-communications.ps1

# Security scan both projects
.\scripts\security\run-zap-both.ps1
```

### Complete Analysis
```powershell
# Full analysis (SonarQube + Security) for specific project
.\scripts\combined\full-analysis-clinics.ps1
.\scripts\combined\full-analysis-communications.ps1

# Full analysis for both projects
.\scripts\combined\full-analysis-both.ps1
```

## 📊 Report Locations

### SonarQube Reports
- **JSON Reports**: `sonar-reports/sonarqube-report-[project].json`
- **Dashboard**: http://localhost:9000 (when SonarQube is running)

### Security Reports
- **ZAP Reports**: `zap-results/zap-[project]-[scantype]-[timestamp].[format]`
- **Formats**: JSON (machine-readable), HTML (human-readable), XML (structured)

## 🔧 Configuration

### SonarQube Tokens
- **Clinics API**: `sqp_9833dac32acc6233060116e797a16d7c314fcf5c`
- **Communications API**: `sqp_0e576c502636aec9dd0b2e1ebba6f68ce1a948f6`

### Service URLs
- **Clinics API**: http://localhost:2222
- **Communications API**: http://localhost:3001

## 📋 Script Parameters

### SonarQube Scripts
```powershell
# Basic usage
.\run-sonar-[project].ps1

# With custom SonarQube server
.\run-sonar-[project].ps1 -SonarUrl "http://custom-sonar:9000"

# Skip API report generation
.\run-sonar-[project].ps1 -SkipApiReport
```

### Security Scripts
```powershell
# Basic usage (baseline scan)
.\run-zap-[project].ps1

# Full comprehensive scan
.\run-zap-[project].ps1 -ScanType "full"

# API-specific scan
.\run-zap-[project].ps1 -ScanType "api"

# Custom timeout
.\run-zap-[project].ps1 -TimeoutMinutes 45
```

### Combined Scripts
```powershell
# Basic usage
.\full-analysis-[project].ps1

# Custom configuration
.\full-analysis-[project].ps1 -ScanType "full" -TimeoutMinutes 30 -SkipSecurity
```

## 🎯 Best Practices

1. **Run Services First**: Ensure both APIs are running before executing scripts
2. **Sequential Execution**: Run one analysis at a time to avoid resource conflicts
3. **Review Reports**: Always review generated reports for actionable insights
4. **Iterative Improvement**: Fix issues and re-run scans to track progress
5. **Archive Results**: Keep historical reports to track quality trends

## 🔍 Troubleshooting

### Common Issues
- **SonarQube Connection**: Ensure SonarQube server is running on port 9000
- **Docker Issues**: Verify Docker Desktop is running for ZAP scans
- **Service Unavailable**: Check if target APIs are accessible
- **Permission Errors**: Run PowerShell as Administrator if needed

### Support
For issues or questions, refer to the main project documentation or create an issue in the repository.
