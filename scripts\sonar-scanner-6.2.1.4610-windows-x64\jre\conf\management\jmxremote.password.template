# ----------------------------------------------------------------------
#           Template for jmxremote.password
#
# o Copy this template to jmxremote.password
# o Set the user/password entries in jmxremote.password
# o Change the permission of jmxremote.password to be accessible
#   only by the owner.
# o The jmxremote.passwords file will be re-written by the server
#   to replace all plain text passwords with hashed passwords when
#   the file is read by the server.
#

##############################################################
#        Password File for Remote JMX Monitoring
##############################################################
#
# Password file for Remote JMX API access to monitoring.  This
# file defines the different roles and their passwords.  The access
# control file (jmxremote.access by default) defines the allowed
# access for each role.  To be functional, a role must have an entry
# in both the password and the access files.
#
# Default location of this file is $JRE/conf/management/jmxremote.password
# You can specify an alternate location by specifying a property in
# the management config file $JRE/conf/management/management.properties
# or by specifying a system property (See that file for details).

##############################################################
#    File format of the jmxremote.password file
##############################################################
#
# The file contains multiple lines where each line is blank,
# a comment (like this one), or a password entry.
#
# password entry follows the below syntax
#   role_name W [clearPassword|hashedPassword]
#
# role_name is any string that does not itself contain spaces or tabs.
# W = spaces or tabs
#
# Passwords can be specified via clear text or via a hash. Clear text password
# is any string that does not contain spaces or tabs. Hashed passwords must
# follow the below format.
# hashedPassword = base64_encoded_64_byte_salt W base64_encoded_hash W hash_algorithm
# where,
#   base64_encoded_64_byte_salt = 64 byte random salt
#   base64_encoded_hash = Hash_algorithm(password + salt)
#   W = spaces or tabs
#   hash_algorithm = Algorithm string specified using the format below
#       https://docs.oracle.com/javase/9/docs/specs/security/standard-names.html#messagedigest-algorithms
#       This is an optional field. If not specified, SHA3-512 will be assumed.
#
# If passwords are in clear, they will be overwritten by their hash if all of
# the below criteria are met.
#   * com.sun.management.jmxremote.password.toHashes property is set to true in
#     management.properties file
#   * the password file is writable
#   * the system security policy allows writing into the password file, if a
#     security manager is configured
#
# In order to change the password for a role, replace the hashed password entry
# with a new clear text password or a new hashed password. If the new password
# is in clear, it will be replaced with its hash when a new login attempt is made.
#
# A given role should have at most one entry in this file.  If a role
# has no entry, it has no access.
# If multiple entries are found for the same role name, then the last one
# is used.
#
# A user generated hashed password file can also be used instead of clear-text
# password file. If generated by the user, hashed passwords must follow the
# format specified above.
#
# Caution: It is recommended not to edit the password file while the
# agent is running, as edits could be lost if a client connection triggers the
# hashing of the password file at the same time that the file is externally modified.
# The integrity of the file is guaranteed, but any external edits made to the
# file during the short period between the time that the agent reads the file
# and the time that it writes it back might get lost

##############################################################
#    File permissions of the jmxremote.password file
##############################################################
#       This file must be made accessible by ONLY the owner,
#       otherwise the program will exit with an error.
#
# In a typical installation, this file can be accessed by anybody on the
# local machine, and possibly by people on other machines.
# For security, you should either restrict the access to this file except for owner,
# or specify another, less accessible file in the management config file
# as described above.
#
# In order to prevent inadverent edits to the password file in the 
# production environment, it is recommended to deploy a read-only 
# hashed password file. The hashed entries for clear passwords can be generated 
# in advance by running the JMX agent.
#

##############################################################
#    Sample of the jmxremote.password file
##############################################################
# Following are two commented-out entries.  The "monitorRole" role has
# password "QED".  The "controlRole" role has password "R&D". This is an example
# of specifying passwords in the clear
#
#   monitorRole  QED
#   controlRole  R&D
# 
# Once a login attempt is made, passwords will be hashed and the file will have 
# below entries with clear passwords overwritten by their respective 
# SHA3-512 hash
#
#   monitorRole trilby APzBTt34rV2l+OMbuvbnOQ4si8UZmfRCVbIY1+fAofV5CkQzXS/FDMGteQQk/R3q1wtt104qImzJEA7gCwl6dw== 4EeTdSJ7X6Imu0Mb+dWqIns7a7QPIBoM3NB/XlpMQSPSicE7PnlALVWn2pBY3Q3pGDHyAb32Hd8GUToQbUhAjA== SHA3-512
#   controlRole roHEJSbRqSSTII4Z4+NOCV2OJaZVQ/dw153Fy2u4ILDP9XiZ426GwzCzc3RtpoqNMwqYIcfdd74xWXSMrWtGaA== w9qDsekgKn0WOVJycDyU0kLBa081zbStcCjUAVEqlfon5Sgx7XHtaodbmzpLegA1jT7Ag36T0zHaEWRHJe2fdA== SHA3-512
# 