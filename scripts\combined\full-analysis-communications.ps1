# Complete Analysis Script for Healtether Communications API
# Performs both SonarQube code quality analysis and ZAP security testing

param(
    [string]$SonarUrl = "http://localhost:9000",
    [string]$ScanType = "baseline",  # Options: baseline, full, api
    [int]$TimeoutMinutes = 30,
    [string]$TargetUrl = "http://host.docker.internal:3001",
    [switch]$SkipSonarQube = $false,
    [switch]$SkipSecurity = $false,
    [switch]$SkipHealthCheck = $false,
    [switch]$OpenDashboards = $false,
    [int]$DelayBetweenAnalyses = 30  # seconds between SonarQube and ZAP
)

Write-Host "🚀 Complete Analysis - Healtether Communications API" -ForegroundColor Green
Write-Host "===================================================" -ForegroundColor Green

$PROJECT_NAME = "Healtether Communications API"
$startTime = Get-Date
$results = @()

# Function to run SonarQube analysis
function Invoke-SonarQubeAnalysis {
    Write-Host "`n📊 Starting SonarQube Code Quality Analysis..." -ForegroundColor Yellow
    Write-Host "=" * 50 -ForegroundColor Yellow
    
    $sonarScript = Join-Path $PSScriptRoot "..\sonarqube\run-sonar-communications.ps1"
    
    if (!(Test-Path $sonarScript)) {
        Write-Host "❌ SonarQube script not found: $sonarScript" -ForegroundColor Red
        return @{Analysis="SonarQube"; Success=$false; Error="Script not found"}
    }
    
    try {
        $scriptArgs = @("-SonarUrl", $SonarUrl)
        if ($OpenDashboards) { $scriptArgs += "-OpenDashboard" }
        
        & $sonarScript @scriptArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ SonarQube analysis completed successfully" -ForegroundColor Green
            return @{Analysis="SonarQube"; Success=$true; Error=$null}
        } else {
            Write-Host "❌ SonarQube analysis failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return @{Analysis="SonarQube"; Success=$false; Error="Exit code: $LASTEXITCODE"}
        }
    }
    catch {
        Write-Host "❌ SonarQube analysis failed with exception" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return @{Analysis="SonarQube"; Success=$false; Error=$_.Exception.Message}
    }
}

# Function to run ZAP security testing
function Invoke-SecurityTesting {
    Write-Host "`n🔒 Starting ZAP Security Testing..." -ForegroundColor Yellow
    Write-Host "=" * 40 -ForegroundColor Yellow
    
    $zapScript = Join-Path $PSScriptRoot "..\security\run-zap-communications.ps1"
    
    if (!(Test-Path $zapScript)) {
        Write-Host "❌ ZAP security script not found: $zapScript" -ForegroundColor Red
        return @{Analysis="Security"; Success=$false; Error="Script not found"}
    }
    
    try {
        $scriptArgs = @(
            "-ScanType", $ScanType,
            "-TimeoutMinutes", $TimeoutMinutes,
            "-TargetUrl", $TargetUrl
        )
        
        if ($SkipHealthCheck) { $scriptArgs += "-SkipHealthCheck" }
        if ($OpenDashboards) { $scriptArgs += "-OpenReport" }
        
        & $zapScript @scriptArgs
        
        if ($LASTEXITCODE -le 1) {  # ZAP returns 0-1 for successful scans
            Write-Host "✅ Security testing completed successfully" -ForegroundColor Green
            return @{Analysis="Security"; Success=$true; Error=$null}
        } else {
            Write-Host "❌ Security testing failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return @{Analysis="Security"; Success=$false; Error="Exit code: $LASTEXITCODE"}
        }
    }
    catch {
        Write-Host "❌ Security testing failed with exception" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return @{Analysis="Security"; Success=$false; Error=$_.Exception.Message}
    }
}

# Main execution
Write-Host "🎯 Starting comprehensive analysis for $PROJECT_NAME" -ForegroundColor Cyan
Write-Host "   SonarQube: $(if ($SkipSonarQube) { "SKIPPED" } else { "ENABLED" })" -ForegroundColor Gray
Write-Host "   Security: $(if ($SkipSecurity) { "SKIPPED" } else { "ENABLED ($ScanType)" })" -ForegroundColor Gray
Write-Host "   Target URL: $TargetUrl" -ForegroundColor Gray

# Run SonarQube analysis
if (!$SkipSonarQube) {
    $result = Invoke-SonarQubeAnalysis
    $results += $result
    
    # Delay between analyses if both are enabled
    if (!$SkipSecurity -and $DelayBetweenAnalyses -gt 0) {
        Write-Host "`n⏳ Waiting $DelayBetweenAnalyses seconds before security testing..." -ForegroundColor Cyan
        Start-Sleep -Seconds $DelayBetweenAnalyses
    }
} else {
    Write-Host "`n⏭️  Skipping SonarQube analysis" -ForegroundColor Yellow
}

# Run security testing
if (!$SkipSecurity) {
    $result = Invoke-SecurityTesting
    $results += $result
} else {
    Write-Host "`n⏭️  Skipping security testing" -ForegroundColor Yellow
}

# Generate comprehensive summary report
Write-Host "`n📊 Generating comprehensive analysis summary..." -ForegroundColor Cyan

$endTime = Get-Date
$duration = $endTime - $startTime
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"

$summaryReport = @"
HEALTETHER COMMUNICATIONS API - COMPREHENSIVE ANALYSIS SUMMARY
=============================================================
Analysis Date: $($startTime.ToString("yyyy-MM-dd HH:mm:ss"))
Duration: $($duration.ToString("hh\:mm\:ss"))
Target URL: $TargetUrl

ANALYSES PERFORMED:
"@

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $summaryReport += "`n- $($result.Analysis): $status"
    if (!$result.Success -and $result.Error) {
        $summaryReport += "`n  Error: $($result.Error)"
    }
}

$summaryReport += @"

GENERATED REPORTS:
"@

if (!$SkipSonarQube) {
    $summaryReport += @"
- SonarQube JSON Report: sonar-reports/sonarqube-report-healtether-communications-api.json
- SonarQube Dashboard: $SonarUrl/dashboard?id=healtether-communications-api
"@
}

if (!$SkipSecurity) {
    $summaryReport += @"
- Security Reports: zap-results/zap-communications-$ScanType-*.{json,html,xml}
"@
}

$summaryReport += @"

NEXT STEPS:
1. Review SonarQube quality gate status and metrics
2. Address critical and major code quality issues
3. Review security scan results for vulnerabilities
4. Prioritize fixes based on risk levels
5. Improve test coverage if below threshold
6. Re-run analyses after implementing fixes

QUALITY FOCUS AREAS:
- Code Quality: Focus on bugs, vulnerabilities, and code smells
- Security: Address high and medium risk vulnerabilities first
- Coverage: Aim for >80% test coverage
- Maintainability: Reduce technical debt and complexity

COMMUNICATIONS API SPECIFIC NOTES:
- Focus on WhatsApp integration security
- Review FHIR data handling and validation
- Ensure proper encryption for sensitive data
- Test webhook endpoints thoroughly
"@

# Save comprehensive summary report
$summaryFile = "analysis-reports\communications-comprehensive-analysis-$timestamp.txt"

# Ensure analysis-reports directory exists
if (!(Test-Path "analysis-reports")) {
    New-Item -ItemType Directory -Path "analysis-reports" | Out-Null
}

$summaryReport | Out-File -FilePath $summaryFile -Encoding UTF8

# Display final results
Write-Host "`n🏁 COMPREHENSIVE ANALYSIS COMPLETED" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host "Project: $PROJECT_NAME" -ForegroundColor Cyan
Write-Host "Duration: $($duration.ToString("hh\:mm\:ss"))" -ForegroundColor Cyan

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    Write-Host "$($result.Analysis): $status" -ForegroundColor $color
    if (!$result.Success -and $result.Error) {
        Write-Host "  Error: $($result.Error)" -ForegroundColor Yellow
    }
}

Write-Host "`n📄 Comprehensive summary: $summaryFile" -ForegroundColor Cyan

if (!$SkipSonarQube) {
    Write-Host "📊 SonarQube dashboard: $SonarUrl/dashboard?id=healtether-communications-api" -ForegroundColor Cyan
}

if (!$SkipSecurity) {
    Write-Host "🔒 Security reports: zap-results/" -ForegroundColor Cyan
}

Write-Host "`n💡 Review all reports and prioritize fixes based on risk and impact" -ForegroundColor Yellow

# Exit with appropriate code
$failedCount = ($results | Where-Object { !$_.Success }).Count
if ($failedCount -gt 0) {
    Write-Host "`n⚠️  $failedCount analysis(es) failed" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "`n🎉 All analyses completed successfully!" -ForegroundColor Green
    exit 0
}
