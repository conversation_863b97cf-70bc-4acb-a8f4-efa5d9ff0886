# SonarQube Analysis Script for Both Healtether Projects
# Performs comprehensive code quality analysis for both Clinics and Communications APIs

param(
    [string]$SonarUrl = "http://localhost:9000",
    [switch]$SkipApiReport = $false,
    [switch]$OpenDashboard = $false,
    [int]$DelayBetweenScans = 30  # seconds between scans
)

Write-Host "🔍 SonarQube Analysis - Both Healtether Projects" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Check if SonarQube server is accessible
Write-Host "🔗 Checking SonarQube server connectivity..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$SonarUrl/api/system/status" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SonarQube server is accessible at $SonarUrl" -ForegroundColor Green
    }
}
catch {
    Write-Host "❌ SonarQube server is not accessible at $SonarUrl" -ForegroundColor Red
    Write-Host "   Please ensure SonarQube is running or check the URL" -ForegroundColor Yellow
    Write-Host "   Start SonarQube: docker-compose -f docker-compose.sonarqube.yml up -d" -ForegroundColor Yellow
    exit 1
}

$startTime = Get-Date
$results = @()

# Function to run individual project analysis
function Invoke-ProjectAnalysis {
    param(
        [string]$ScriptPath,
        [string]$ProjectName
    )
    
    Write-Host "`n🎯 Starting analysis for $ProjectName..." -ForegroundColor Yellow
    Write-Host "=" * 50 -ForegroundColor Yellow
    
    try {
        $scriptArgs = @()
        if ($SkipApiReport) { $scriptArgs += "-SkipApiReport" }
        
        & $ScriptPath @scriptArgs -SonarUrl $SonarUrl
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $ProjectName analysis completed successfully" -ForegroundColor Green
            return @{Project=$ProjectName; Success=$true; Error=$null}
        } else {
            Write-Host "❌ $ProjectName analysis failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return @{Project=$ProjectName; Success=$false; Error="Exit code: $LASTEXITCODE"}
        }
    }
    catch {
        Write-Host "❌ $ProjectName analysis failed with exception" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return @{Project=$ProjectName; Success=$false; Error=$_.Exception.Message}
    }
}

# Run Clinics API analysis
$clinicsScript = Join-Path $PSScriptRoot "run-sonar-clinics.ps1"
if (Test-Path $clinicsScript) {
    $result = Invoke-ProjectAnalysis -ScriptPath $clinicsScript -ProjectName "Healtether Clinics API"
    $results += $result
} else {
    Write-Host "❌ Clinics analysis script not found: $clinicsScript" -ForegroundColor Red
    $results += @{Project="Healtether Clinics API"; Success=$false; Error="Script not found"}
}

# Delay between scans to avoid resource conflicts
if ($DelayBetweenScans -gt 0) {
    Write-Host "`n⏳ Waiting $DelayBetweenScans seconds before next scan..." -ForegroundColor Cyan
    Start-Sleep -Seconds $DelayBetweenScans
}

# Run Communications API analysis
$communicationsScript = Join-Path $PSScriptRoot "run-sonar-communications.ps1"
if (Test-Path $communicationsScript) {
    $result = Invoke-ProjectAnalysis -ScriptPath $communicationsScript -ProjectName "Healtether Communications API"
    $results += $result
} else {
    Write-Host "❌ Communications analysis script not found: $communicationsScript" -ForegroundColor Red
    $results += @{Project="Healtether Communications API"; Success=$false; Error="Script not found"}
}

# Generate combined summary report
Write-Host "`n📊 Generating combined summary report..." -ForegroundColor Cyan

$endTime = Get-Date
$duration = $endTime - $startTime

$summaryReport = @"
HEALTETHER SONARQUBE ANALYSIS SUMMARY
====================================
Analysis Date: $($startTime.ToString("yyyy-MM-dd HH:mm:ss"))
Duration: $($duration.ToString("hh\:mm\:ss"))
SonarQube Server: $SonarUrl

PROJECTS ANALYZED:
"@

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $summaryReport += "`n- $($result.Project): $status"
    if (!$result.Success -and $result.Error) {
        $summaryReport += "`n  Error: $($result.Error)"
    }
}

$summaryReport += @"

REPORTS GENERATED:
- sonar-reports/sonarqube-report-healtether-clinics-api.json
- sonar-reports/sonarqube-report-healtether-communications-api.json

DASHBOARDS:
- Clinics API: $SonarUrl/dashboard?id=healtether-clinics-api
- Communications API: $SonarUrl/dashboard?id=healtether-communications-api

NEXT STEPS:
1. Review quality gate status for both projects
2. Compare metrics and identify priority areas
3. Address critical and major issues first
4. Focus on improving test coverage
5. Re-run analysis after fixes to track progress
"@

# Save summary report
$summaryFile = "sonar-reports\combined-analysis-summary-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
$summaryReport | Out-File -FilePath $summaryFile -Encoding UTF8

# Display final results
Write-Host "`n🏁 COMBINED ANALYSIS COMPLETED" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green
Write-Host "Duration: $($duration.ToString("hh\:mm\:ss"))" -ForegroundColor Cyan

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    Write-Host "$($result.Project): $status" -ForegroundColor $color
    if (!$result.Success -and $result.Error) {
        Write-Host "  Error: $($result.Error)" -ForegroundColor Yellow
    }
}

Write-Host "`n📄 Summary report: $summaryFile" -ForegroundColor Cyan
Write-Host "📊 Individual reports in: sonar-reports/" -ForegroundColor Cyan

# Open dashboards if requested
if ($OpenDashboard) {
    Write-Host "`n🌐 Opening SonarQube dashboards..." -ForegroundColor Cyan
    Start-Process "$SonarUrl/dashboard?id=healtether-clinics-api"
    Start-Sleep -Seconds 2
    Start-Process "$SonarUrl/dashboard?id=healtether-communications-api"
}

# Exit with appropriate code
$failedCount = ($results | Where-Object { !$_.Success }).Count
if ($failedCount -gt 0) {
    Write-Host "`n⚠️  $failedCount project(s) failed analysis" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "`n🎉 All projects analyzed successfully!" -ForegroundColor Green
    exit 0
}
