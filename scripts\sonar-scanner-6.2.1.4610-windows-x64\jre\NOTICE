# Notices for Eclipse Temurin

This content is produced and maintained by the Eclipse Temurin project.

 * Project home: https://projects.eclipse.org/projects/adoptium.temurin

## Trademarks

Eclipse Temurin is a trademark of the Eclipse Foundation. Eclipse, and the
Eclipse Logo are registered trademarks of the Eclipse Foundation.

Java and all Java-based trademarks are trademarks of Oracle Corporation in
the United States, other countries, or both.

## Copyright

All content is the property of the respective authors or their employers.
For more information regarding authorship of content, please consult the
listed source code repository logs.

## Declared Project Licenses

This program and the accompanying materials are made available under the terms
of the GNU General Public License, version 2, with the Classpath Exception.

Additional information relating to the program and accompanying materials
license and usage is available as follows.
 * For Eclipse Temurin version 8 see the LICENSE and ASSEMBLY_EXCEPTION files
in the top level directory of the installation.
 * For Eclipse Temurin version 9 or later see the files under the legal/
directory in the top level directory of the installation.

SPDX-License-Identifier: GPL-2.0 WITH Classpath-exception-2.0

## Source Code

The project maintains the following source code repositories which may be
relevant to this content:

 * https://github.com/adoptium/temurin-build
 * https://github.com/adoptium/jdk
 * https://github.com/adoptium/jdk8u
 * https://github.com/adoptium/jdk11u
 * https://github.com/adoptium/jdk17u
 * https://github.com/adoptium/jdk20
 * and so on

## Third-party Content

This program and accompanying materials contains third-party content.
 * For Eclipse Temurin version 8 see the THIRD_PARTY_LICENSE file in the
top level directory of the installation.
 * For Eclipse Temurin version 9 or later see the files under the legal/
directory in the top level directory of the installation.

## Cryptography

Content may contain encryption software. The country in which you are currently
may have restrictions on the import, possession, and use, and/or re-export to
another country, of encryption software. BEFORE using any encryption software,
please check the country's laws, regulations and policies concerning the import,
possession, or use, and re-export of encryption software, to see if this is
permitted.
