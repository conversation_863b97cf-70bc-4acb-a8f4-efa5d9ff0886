#!/usr/bin/env node

/**
 * SonarQube API Client for Healtether Projects
 * Fetches comprehensive analysis data from SonarQube API
 * 
 * Usage:
 *   node sonarqube-api-client.js [project-key]
 *   node sonarqube-api-client.js healtether-communications-api
 *   node sonarqube-api-client.js healtether-clinics-api
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const SONARQUBE_CONFIG = {
    baseURL: 'http://localhost:9000',
    // Authentication tokens from memories
    tokens: {
        'healtether-communications-api': 'sqp_0e576c502636aec9dd0b2e1ebba6f68ce1a948f6',
        'healtether-clinics-api': 'sqp_9833dac32acc6233060116e797a16d7c314fcf5c'
    },
    projects: {
        'healtether-communications-api': 'Healtether Communications API',
        'healtether-clinics-api': 'Healtether Clinics API'
    }
};

class SonarQubeAPIClient {
    constructor(projectKey) {
        this.projectKey = projectKey;
        this.token = SONARQUBE_CONFIG.tokens[projectKey];
        this.projectName = SONARQUBE_CONFIG.projects[projectKey];
        
        if (!this.token) {
            throw new Error(`No authentication token found for project: ${projectKey}`);
        }

        this.client = axios.create({
            baseURL: SONARQUBE_CONFIG.baseURL,
            auth: {
                username: this.token,
                password: ''
            },
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    async fetchProjectStatus() {
        try {
            const response = await this.client.get(`/api/qualitygates/project_status`, {
                params: { projectKey: this.projectKey }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching project status:', error.message);
            return null;
        }
    }

    async fetchMeasures() {
        try {
            const metrics = [
                'alert_status', 'quality_gate_details',
                'bugs', 'vulnerabilities', 'security_hotspots', 'code_smells',
                'coverage', 'duplicated_lines_density',
                'ncloc', 'complexity', 'cognitive_complexity',
                'reliability_rating', 'security_rating', 'sqale_rating',
                'sqale_index', 'sqale_debt_ratio',
                'new_bugs', 'new_vulnerabilities', 'new_security_hotspots', 'new_code_smells',
                'new_coverage', 'new_duplicated_lines_density',
                'new_lines', 'new_reliability_rating', 'new_security_rating', 'new_maintainability_rating'
            ];

            const response = await this.client.get(`/api/measures/component`, {
                params: {
                    component: this.projectKey,
                    metricKeys: metrics.join(',')
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching measures:', error.message);
            return null;
        }
    }

    async fetchIssues(type = 'all', pageSize = 500) {
        try {
            // Use token auth (token as username, empty password)
            const tokenClient = axios.create({
                baseURL: SONARQUBE_CONFIG.baseURL,
                auth: {
                    username: this.token,
                    password: ''
                },
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const params = {
                componentKeys: this.projectKey,
                ps: pageSize,
                additionalFields: 'rules'
            };

            if (type !== 'all') {
                params.types = type.toUpperCase();
            }

            console.log(`Fetching ${type} issues with params:`, params);
            const response = await tokenClient.get(`/api/issues/search`, { params });
            console.log(`Successfully fetched ${response.data.total} issues`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching ${type} issues:`, error.response?.status, error.response?.data || error.message);
            return null;
        }
    }

    async fetchSecurityHotspots() {
        try {
            // Use token auth for security hotspots API
            const tokenClient = axios.create({
                baseURL: SONARQUBE_CONFIG.baseURL,
                auth: {
                    username: this.token,
                    password: ''
                },
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const params = {
                projectKey: this.projectKey,
                ps: 500
            };

            console.log('Fetching security hotspots with params:', params);
            const response = await tokenClient.get(`/api/hotspots/search`, { params });
            console.log(`Successfully fetched ${response.data.hotspots?.length || 0} security hotspots`);
            return response.data;
        } catch (error) {
            console.error('Error fetching security hotspots:', error.response?.status, error.response?.data || error.message);
            return null;
        }
    }

    async fetchDuplicatedBlocks() {
        try {
            const response = await this.client.get(`/api/duplications/show`, {
                params: {
                    key: this.projectKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching duplicated blocks:', error.message);
            return null;
        }
    }

    async fetchProjectAnalyses() {
        try {
            const response = await this.client.get(`/api/project_analyses/search`, {
                params: {
                    project: this.projectKey,
                    ps: 10
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching project analyses:', error.message);
            return null;
        }
    }

    formatMetric(metric) {
        const value = metric.value;
        const metricKey = metric.metric;

        // Format different types of metrics
        switch (metricKey) {
            case 'coverage':
            case 'new_coverage':
            case 'duplicated_lines_density':
            case 'new_duplicated_lines_density':
            case 'sqale_debt_ratio':
                return `${value}%`;
            
            case 'sqale_index':
                const hours = Math.floor(value / 60);
                const minutes = value % 60;
                return `${hours}h ${minutes}m`;
            
            case 'reliability_rating':
            case 'security_rating':
            case 'sqale_rating':
            case 'new_reliability_rating':
            case 'new_security_rating':
            case 'new_maintainability_rating':
                const ratings = ['A', 'B', 'C', 'D', 'E'];
                return ratings[parseInt(value) - 1] || value;
            
            default:
                return value;
        }
    }

    generateReport(data) {
        const timestamp = new Date().toISOString();
        const report = {
            project: {
                key: this.projectKey,
                name: this.projectName,
                analysisTime: timestamp
            },
            qualityGate: data.projectStatus,
            metrics: {},
            issues: {
                summary: {},
                details: data.issues
            },
            securityHotspots: data.securityHotspots,
            analyses: data.analyses
        };

        // Process measures
        if (data.measures && data.measures.component && data.measures.component.measures) {
            data.measures.component.measures.forEach(measure => {
                report.metrics[measure.metric] = {
                    value: measure.value,
                    formatted: this.formatMetric(measure)
                };
            });
        }

        // Process issues summary
        if (data.issues) {
            report.issues.summary = {
                total: data.issues.total,
                bugs: data.issues.issues.filter(i => i.type === 'BUG').length,
                vulnerabilities: data.issues.issues.filter(i => i.type === 'VULNERABILITY').length,
                codeSmells: data.issues.issues.filter(i => i.type === 'CODE_SMELL').length,
                severities: {
                    blocker: data.issues.issues.filter(i => i.severity === 'BLOCKER').length,
                    critical: data.issues.issues.filter(i => i.severity === 'CRITICAL').length,
                    major: data.issues.issues.filter(i => i.severity === 'MAJOR').length,
                    minor: data.issues.issues.filter(i => i.severity === 'MINOR').length,
                    info: data.issues.issues.filter(i => i.severity === 'INFO').length
                }
            };
        }

        return report;
    }

    async generateFullReport() {
        console.log(`🔍 Fetching SonarQube analysis data for: ${this.projectName}`);
        console.log(`📊 Project Key: ${this.projectKey}`);
        console.log('━'.repeat(80));

        const data = {};

        // Fetch all data in parallel
        const [projectStatus, measures, issues, securityHotspots, analyses] = await Promise.all([
            this.fetchProjectStatus(),
            this.fetchMeasures(),
            this.fetchIssues(),
            this.fetchSecurityHotspots(),
            this.fetchProjectAnalyses()
        ]);

        data.projectStatus = projectStatus;
        data.measures = measures;
        data.issues = issues;
        data.securityHotspots = securityHotspots;
        data.analyses = analyses;

        const report = this.generateReport(data);

        // Ensure sonar-reports directory exists
        const reportsDir = 'sonar-reports';
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        // Save detailed report to file in sonar-reports folder
        const reportFile = `${reportsDir}/sonarqube-report-${this.projectKey}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

        console.log(`📄 Detailed report saved to: ${reportFile}`);
        
        return report;
    }

    printSummary(report) {
        console.log('\n🎯 QUALITY GATE STATUS');
        console.log('━'.repeat(50));
        if (report.qualityGate && report.qualityGate.projectStatus) {
            const status = report.qualityGate.projectStatus.status;
            const emoji = status === 'OK' ? '✅' : '❌';
            console.log(`${emoji} Status: ${status}`);
            
            if (report.qualityGate.projectStatus.conditions) {
                report.qualityGate.projectStatus.conditions.forEach(condition => {
                    const conditionEmoji = condition.status === 'OK' ? '✅' : '❌';
                    console.log(`  ${conditionEmoji} ${condition.metricKey}: ${condition.actualValue} (threshold: ${condition.errorThreshold})`);
                });
            }
        }

        console.log('\n📊 KEY METRICS');
        console.log('━'.repeat(50));
        const keyMetrics = [
            'bugs', 'vulnerabilities', 'security_hotspots', 'code_smells',
            'coverage', 'duplicated_lines_density', 'ncloc',
            'reliability_rating', 'security_rating', 'sqale_rating'
        ];

        keyMetrics.forEach(metric => {
            if (report.metrics[metric]) {
                console.log(`${metric.padEnd(25)}: ${report.metrics[metric].formatted}`);
            }
        });

        console.log('\n🐛 ISSUES SUMMARY');
        console.log('━'.repeat(50));
        if (report.issues.summary) {
            const summary = report.issues.summary;
            console.log(`Total Issues: ${summary.total}`);
            console.log(`  🐛 Bugs: ${summary.bugs}`);
            console.log(`  🔒 Vulnerabilities: ${summary.vulnerabilities}`);
            console.log(`  💨 Code Smells: ${summary.codeSmells}`);
            
            console.log('\nBy Severity:');
            Object.entries(summary.severities).forEach(([severity, count]) => {
                if (count > 0) {
                    console.log(`  ${severity.toUpperCase()}: ${count}`);
                }
            });
        }

        if (report.securityHotspots && report.securityHotspots.hotspots) {
            console.log(`\n🔥 Security Hotspots: ${report.securityHotspots.hotspots.length}`);
        }
    }

    printDetailedIssues(report) {
        console.log('\n📋 DETAILED ISSUES BREAKDOWN');
        console.log('━'.repeat(80));

        if (report.issues && report.issues.details && report.issues.details.issues) {
            const issues = report.issues.details.issues;

            // Group issues by type
            const bugIssues = issues.filter(i => i.type === 'BUG');
            const vulnerabilityIssues = issues.filter(i => i.type === 'VULNERABILITY');
            const codeSmellIssues = issues.filter(i => i.type === 'CODE_SMELL');

            // Print bugs
            if (bugIssues.length > 0) {
                console.log(`\n🐛 BUGS (${bugIssues.length}):`);
                bugIssues.slice(0, 10).forEach((issue, index) => {
                    console.log(`  ${index + 1}. [${issue.severity}] ${issue.message}`);
                    console.log(`     📁 ${issue.component} (Line ${issue.line || 'N/A'})`);
                    console.log(`     🔧 Rule: ${issue.rule}`);
                });
                if (bugIssues.length > 10) {
                    console.log(`     ... and ${bugIssues.length - 10} more bugs`);
                }
            }

            // Print vulnerabilities
            if (vulnerabilityIssues.length > 0) {
                console.log(`\n🔒 VULNERABILITIES (${vulnerabilityIssues.length}):`);
                vulnerabilityIssues.slice(0, 10).forEach((issue, index) => {
                    console.log(`  ${index + 1}. [${issue.severity}] ${issue.message}`);
                    console.log(`     📁 ${issue.component} (Line ${issue.line || 'N/A'})`);
                    console.log(`     🔧 Rule: ${issue.rule}`);
                });
                if (vulnerabilityIssues.length > 10) {
                    console.log(`     ... and ${vulnerabilityIssues.length - 10} more vulnerabilities`);
                }
            }

            // Print top code smells
            if (codeSmellIssues.length > 0) {
                console.log(`\n💨 TOP CODE SMELLS (showing 15 of ${codeSmellIssues.length}):`);
                codeSmellIssues.slice(0, 15).forEach((issue, index) => {
                    console.log(`  ${index + 1}. [${issue.severity}] ${issue.message}`);
                    console.log(`     📁 ${issue.component} (Line ${issue.line || 'N/A'})`);
                    console.log(`     🔧 Rule: ${issue.rule}`);
                });
                if (codeSmellIssues.length > 15) {
                    console.log(`     ... and ${codeSmellIssues.length - 15} more code smells`);
                }
            }
        }

        // Print security hotspots details
        if (report.securityHotspots && report.securityHotspots.hotspots) {
            const hotspots = report.securityHotspots.hotspots;
            console.log(`\n🔥 SECURITY HOTSPOTS (showing 10 of ${hotspots.length}):`);
            hotspots.slice(0, 10).forEach((hotspot, index) => {
                console.log(`  ${index + 1}. [${hotspot.vulnerabilityProbability}] ${hotspot.message}`);
                console.log(`     📁 ${hotspot.component} (Line ${hotspot.line || 'N/A'})`);
                console.log(`     🔧 Rule: ${hotspot.rule}`);
                console.log(`     📊 Status: ${hotspot.status}`);
            });
            if (hotspots.length > 10) {
                console.log(`     ... and ${hotspots.length - 10} more security hotspots`);
            }
        }
    }
}

// Main execution
async function main() {
    const projectKey = process.argv[2] || 'healtether-communications-api';
    
    if (!SONARQUBE_CONFIG.projects[projectKey]) {
        console.error(`❌ Unknown project key: ${projectKey}`);
        console.log('Available projects:');
        Object.keys(SONARQUBE_CONFIG.projects).forEach(key => {
            console.log(`  - ${key}`);
        });
        process.exit(1);
    }

    try {
        const client = new SonarQubeAPIClient(projectKey);
        const report = await client.generateFullReport();
        client.printSummary(report);
        client.printDetailedIssues(report);

        console.log('\n✅ Analysis complete!');
        console.log(`📊 View detailed data in the generated JSON report file`);
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = SonarQubeAPIClient;
