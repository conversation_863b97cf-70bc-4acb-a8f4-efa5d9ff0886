version: '3.8'

services:
  # OWASP ZAP for security testing
  zap:
    image: ghcr.io/zaproxy/zaproxy:stable
    container_name: healtether-zap-scanner
    volumes:
      - ./zap-results:/zap/wrk/:rw
    networks:
      - healtether-security
    profiles:
      - security-testing
    command: >
      sh -c "
        echo 'Starting OWASP ZAP Security Scanner for Healtether APIs...' &&
        echo 'Testing Healtether.Clinics API (Port 2222)...' &&
        zap-baseline.py -t http://host.docker.internal:2222 -J clinics-baseline.json -H clinics-baseline.html -r clinics-baseline.xml &&
        echo 'Testing Healtether.Communications API (Port 3001)...' &&
        zap-baseline.py -t http://host.docker.internal:3001 -J communications-baseline.json -H communications-baseline.html -r communications-baseline.xml &&
        echo 'Running API-specific scans...' &&
        zap-api-scan.py -t http://host.docker.internal:2222 -J clinics-api.json -H clinics-api.html -r clinics-api.xml &&
        zap-api-scan.py -t http://host.docker.internal:3001 -J communications-api.json -H communications-api.html -r communications-api.xml &&
        echo 'Security scanning completed! Check zap-results directory for reports.'
      "

  # ZAP with GUI for interactive testing (optional)
  zap-gui:
    image: ghcr.io/zaproxy/zaproxy:stable
    container_name: healtether-zap-gui
    ports:
      - "8080:8080"
      - "8090:8090"
    volumes:
      - ./zap-results:/zap/wrk/:rw
    networks:
      - healtether-security
    profiles:
      - gui-testing
    command: zap-webswing.sh
    environment:
      - ZAP_PORT=8080

networks:
  healtether-security:
    driver: bridge
