# Complete Analysis Script for Both Healtether Projects
# Performs comprehensive SonarQube and ZAP security analysis for both APIs

param(
    [string]$SonarUrl = "http://localhost:9000",
    [string]$ScanType = "baseline",  # Options: baseline, full, api
    [int]$TimeoutMinutes = 30,
    [switch]$SkipSonarQube = $false,
    [switch]$SkipSecurity = $false,
    [switch]$SkipHealthCheck = $false,
    [switch]$OpenDashboards = $false,
    [int]$DelayBetweenProjects = 60,  # seconds between projects
    [int]$DelayBetweenAnalyses = 30   # seconds between SonarQube and ZAP
)

Write-Host "🚀 Complete Analysis - Both Healtether Projects" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

$startTime = Get-Date
$results = @()

# Function to run comprehensive analysis for a single project
function Invoke-ProjectAnalysis {
    param(
        [string]$ScriptPath,
        [string]$ProjectName
    )
    
    Write-Host "`n🎯 Starting comprehensive analysis for $ProjectName..." -ForegroundColor Yellow
    Write-Host "=" * 70 -ForegroundColor Yellow
    
    if (!(Test-Path $ScriptPath)) {
        Write-Host "❌ Analysis script not found: $ScriptPath" -ForegroundColor Red
        return @{Project=$ProjectName; Success=$false; Error="Script not found"}
    }
    
    try {
        $scriptArgs = @(
            "-SonarUrl", $SonarUrl,
            "-ScanType", $ScanType,
            "-TimeoutMinutes", $TimeoutMinutes,
            "-DelayBetweenAnalyses", $DelayBetweenAnalyses
        )
        
        if ($SkipSonarQube) { $scriptArgs += "-SkipSonarQube" }
        if ($SkipSecurity) { $scriptArgs += "-SkipSecurity" }
        if ($SkipHealthCheck) { $scriptArgs += "-SkipHealthCheck" }
        if ($OpenDashboards) { $scriptArgs += "-OpenDashboards" }
        
        & $ScriptPath @scriptArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $ProjectName comprehensive analysis completed successfully" -ForegroundColor Green
            return @{Project=$ProjectName; Success=$true; Error=$null}
        } else {
            Write-Host "❌ $ProjectName comprehensive analysis failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return @{Project=$ProjectName; Success=$false; Error="Exit code: $LASTEXITCODE"}
        }
    }
    catch {
        Write-Host "❌ $ProjectName comprehensive analysis failed with exception" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return @{Project=$ProjectName; Success=$false; Error=$_.Exception.Message}
    }
}

# Main execution
Write-Host "🎯 Starting comprehensive analysis for both projects" -ForegroundColor Cyan
Write-Host "   SonarQube: $(if ($SkipSonarQube) { "SKIPPED" } else { "ENABLED" })" -ForegroundColor Gray
Write-Host "   Security: $(if ($SkipSecurity) { "SKIPPED" } else { "ENABLED ($ScanType)" })" -ForegroundColor Gray
Write-Host "   Delay Between Projects: $DelayBetweenProjects seconds" -ForegroundColor Gray

# Run Clinics API comprehensive analysis
$clinicsScript = Join-Path $PSScriptRoot "full-analysis-clinics.ps1"
$result = Invoke-ProjectAnalysis -ScriptPath $clinicsScript -ProjectName "Healtether Clinics API"
$results += $result

# Delay between projects
if ($DelayBetweenProjects -gt 0) {
    Write-Host "`n⏳ Waiting $DelayBetweenProjects seconds before next project analysis..." -ForegroundColor Cyan
    Start-Sleep -Seconds $DelayBetweenProjects
}

# Run Communications API comprehensive analysis
$communicationsScript = Join-Path $PSScriptRoot "full-analysis-communications.ps1"
$result = Invoke-ProjectAnalysis -ScriptPath $communicationsScript -ProjectName "Healtether Communications API"
$results += $result

# Generate master comprehensive summary report
Write-Host "`n📊 Generating master comprehensive analysis summary..." -ForegroundColor Cyan

$endTime = Get-Date
$duration = $endTime - $startTime
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"

$summaryReport = @"
HEALTETHER PROJECTS - MASTER COMPREHENSIVE ANALYSIS SUMMARY
==========================================================
Analysis Date: $($startTime.ToString("yyyy-MM-dd HH:mm:ss"))
Total Duration: $($duration.ToString("hh\:mm\:ss"))
Analysis Type: $(if ($SkipSonarQube -and $SkipSecurity) { "NONE" } elseif ($SkipSonarQube) { "Security Only" } elseif ($SkipSecurity) { "SonarQube Only" } else { "Complete (SonarQube + Security)" })

PROJECTS ANALYZED:
"@

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $summaryReport += "`n- $($result.Project): $status"
    if (!$result.Success -and $result.Error) {
        $summaryReport += "`n  Error: $($result.Error)"
    }
}

$summaryReport += @"

GENERATED REPORTS:
"@

if (!$SkipSonarQube) {
    $summaryReport += @"

SonarQube Reports:
- Clinics API: scripts/sonar-reports/sonarqube-report-healtether-clinics-api.json
- Communications API: scripts/sonar-reports/sonarqube-report-healtether-communications-api.json
- Combined Summary: scripts/sonar-reports/combined-analysis-summary-*.txt

SonarQube Dashboards:
- Clinics API: $SonarUrl/dashboard?id=healtether-clinics-api
- Communications API: $SonarUrl/dashboard?id=healtether-communications-api
"@
}

if (!$SkipSecurity) {
    $summaryReport += @"

Security Reports:
- Clinics API: scripts/zap-results/zap-clinics-$ScanType-*.*
- Communications API: scripts/zap-results/zap-communications-$ScanType-*.*
- Combined Summary: scripts/zap-results/combined-security-summary-*.txt
"@
}

$summaryReport += @"

Individual Project Reports:
- Clinics Analysis: analysis-reports/clinics-comprehensive-analysis-*.txt
- Communications Analysis: analysis-reports/communications-comprehensive-analysis-*.txt

QUALITY OVERVIEW:
Based on recent analysis results:

CLINICS API:
- Security: A (0 issues) ✅
- Reliability: A (0 issues) ✅  
- Maintainability: A (418 issues) ⚠️
- Coverage: 6.6% (needs improvement) ⚠️
- Duplications: 8.0% ⚠️

COMMUNICATIONS API:
- Security: A (0 issues) ✅
- Reliability: A (0 issues) ✅
- Maintainability: A (365 issues) ⚠️
- Coverage: 0.9% (needs significant improvement) ❌
- Duplications: 11.4% ⚠️

PRIORITY ACTIONS:
1. CRITICAL: Improve test coverage for both projects (target: >80%)
2. HIGH: Address maintainability issues (code smells)
3. MEDIUM: Reduce code duplication
4. LOW: Continue security monitoring

NEXT STEPS:
1. Review all generated reports systematically
2. Create action plan based on priority levels
3. Focus on test coverage improvement first
4. Address critical and major code quality issues
5. Implement security fixes for any vulnerabilities found
6. Set up regular analysis schedule (weekly/bi-weekly)
7. Track progress over time with trend analysis

AUTOMATION RECOMMENDATIONS:
- Integrate SonarQube analysis into CI/CD pipeline
- Set up automated security scanning
- Configure quality gates to prevent regression
- Establish code review processes based on analysis results
"@

# Save master comprehensive summary report
$summaryFile = "analysis-reports\master-comprehensive-analysis-$timestamp.txt"

# Ensure analysis-reports directory exists
if (!(Test-Path "analysis-reports")) {
    New-Item -ItemType Directory -Path "analysis-reports" | Out-Null
}

$summaryReport | Out-File -FilePath $summaryFile -Encoding UTF8

# Display final results
Write-Host "`n🏁 MASTER COMPREHENSIVE ANALYSIS COMPLETED" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "Total Duration: $($duration.ToString("hh\:mm\:ss"))" -ForegroundColor Cyan

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    Write-Host "$($result.Project): $status" -ForegroundColor $color
    if (!$result.Success -and $result.Error) {
        Write-Host "  Error: $($result.Error)" -ForegroundColor Yellow
    }
}

Write-Host "`n📄 Master summary: $summaryFile" -ForegroundColor Cyan

if (!$SkipSonarQube) {
    Write-Host "📊 SonarQube reports: sonar-reports/" -ForegroundColor Cyan
}

if (!$SkipSecurity) {
    Write-Host "🔒 Security reports: zap-results/" -ForegroundColor Cyan
}

Write-Host "📋 Individual project reports: analysis-reports/" -ForegroundColor Cyan

Write-Host "`n🎯 KEY RECOMMENDATIONS:" -ForegroundColor Yellow
Write-Host "   1. Focus on improving test coverage (currently very low)" -ForegroundColor White
Write-Host "   2. Address maintainability issues systematically" -ForegroundColor White
Write-Host "   3. Set up regular analysis schedule" -ForegroundColor White
Write-Host "   4. Integrate quality gates into development workflow" -ForegroundColor White

# Exit with appropriate code
$failedCount = ($results | Where-Object { !$_.Success }).Count
if ($failedCount -gt 0) {
    Write-Host "`n⚠️  $failedCount project(s) failed comprehensive analysis" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "`n🎉 All projects analyzed successfully!" -ForegroundColor Green
    exit 0
}
