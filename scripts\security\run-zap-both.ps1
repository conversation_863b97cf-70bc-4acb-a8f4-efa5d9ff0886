# ZAP Security Testing Script for Both Healtether Projects
# Performs comprehensive vulnerability scanning for both Clinics and Communications APIs

param(
    [string]$ScanType = "baseline",  # Options: baseline, full, api
    [int]$TimeoutMinutes = 30,
    [switch]$SkipHealthCheck = $false,
    [switch]$OpenReports = $false,
    [int]$DelayBetweenScans = 60  # seconds between scans
)

Write-Host "🔒 ZAP Security Testing - Both Healtether Projects" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Configuration
$CLINICS_URL = "http://host.docker.internal:2222"
$COMMUNICATIONS_URL = "http://host.docker.internal:3001"
$RESULTS_DIR = "zap-results"
$TIMESTAMP = Get-Date -Format "yyyyMMdd-HHmmss"

# Create results directory
if (!(Test-Path $RESULTS_DIR)) {
    New-Item -ItemType Directory -Path $RESULTS_DIR | Out-Null
    Write-Host "✅ Created results directory: $RESULTS_DIR" -ForegroundColor Green
}

# Function to check Docker availability
function Test-DockerAvailability {
    Write-Host "🐳 Checking Docker availability..." -ForegroundColor Cyan
    
    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker is available: $dockerVersion" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ Docker is not available" -ForegroundColor Red
        Write-Host "   Please install Docker Desktop and ensure it's running" -ForegroundColor Yellow
        return $false
    }
}

# Function to pull ZAP Docker image
function Get-ZapDockerImage {
    Write-Host "📥 Pulling OWASP ZAP Docker image..." -ForegroundColor Cyan
    
    try {
        docker pull ghcr.io/zaproxy/zaproxy:stable
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ ZAP Docker image ready" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to pull ZAP Docker image" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error pulling ZAP Docker image" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to run individual project security scan
function Invoke-ProjectSecurityScan {
    param(
        [string]$ScriptPath,
        [string]$ProjectName,
        [string]$TargetUrl
    )
    
    Write-Host "`n🎯 Starting security scan for $ProjectName..." -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Yellow
    
    try {
        $scriptArgs = @(
            "-ScanType", $ScanType,
            "-TimeoutMinutes", $TimeoutMinutes,
            "-TargetUrl", $TargetUrl
        )
        
        if ($SkipHealthCheck) { $scriptArgs += "-SkipHealthCheck" }
        
        & $ScriptPath @scriptArgs
        
        if ($LASTEXITCODE -le 1) {  # ZAP returns 0-1 for successful scans
            Write-Host "✅ $ProjectName security scan completed" -ForegroundColor Green
            return @{Project=$ProjectName; Success=$true; Error=$null}
        } else {
            Write-Host "❌ $ProjectName security scan failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            return @{Project=$ProjectName; Success=$false; Error="Exit code: $LASTEXITCODE"}
        }
    }
    catch {
        Write-Host "❌ $ProjectName security scan failed with exception" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return @{Project=$ProjectName; Success=$false; Error=$_.Exception.Message}
    }
}

# Main execution
$startTime = Get-Date
$results = @()

Write-Host "🚀 Starting comprehensive security analysis" -ForegroundColor Cyan
Write-Host "   Scan Type: $ScanType" -ForegroundColor Gray
Write-Host "   Timeout: $TimeoutMinutes minutes per scan" -ForegroundColor Gray
Write-Host "   Delay Between Scans: $DelayBetweenScans seconds" -ForegroundColor Gray

# Pre-flight checks
Write-Host "`n🔍 Running pre-flight checks..." -ForegroundColor Cyan

$checksPass = $true

if (!(Test-DockerAvailability)) {
    $checksPass = $false
}

if (!(Get-ZapDockerImage)) {
    $checksPass = $false
}

if (!$checksPass) {
    Write-Host "`n❌ Pre-flight checks failed. Cannot proceed with security scans." -ForegroundColor Red
    exit 1
}

# Run Clinics API security scan
$clinicsScript = Join-Path $PSScriptRoot "run-zap-clinics.ps1"
if (Test-Path $clinicsScript) {
    $result = Invoke-ProjectSecurityScan -ScriptPath $clinicsScript -ProjectName "Healtether Clinics API" -TargetUrl $CLINICS_URL
    $results += $result
} else {
    Write-Host "❌ Clinics security script not found: $clinicsScript" -ForegroundColor Red
    $results += @{Project="Healtether Clinics API"; Success=$false; Error="Script not found"}
}

# Delay between scans to avoid resource conflicts
if ($DelayBetweenScans -gt 0) {
    Write-Host "`n⏳ Waiting $DelayBetweenScans seconds before next scan..." -ForegroundColor Cyan
    Start-Sleep -Seconds $DelayBetweenScans
}

# Run Communications API security scan
$communicationsScript = Join-Path $PSScriptRoot "run-zap-communications.ps1"
if (Test-Path $communicationsScript) {
    $result = Invoke-ProjectSecurityScan -ScriptPath $communicationsScript -ProjectName "Healtether Communications API" -TargetUrl $COMMUNICATIONS_URL
    $results += $result
} else {
    Write-Host "❌ Communications security script not found: $communicationsScript" -ForegroundColor Red
    $results += @{Project="Healtether Communications API"; Success=$false; Error="Script not found"}
}

# Generate combined security summary report
Write-Host "`n📊 Generating combined security summary report..." -ForegroundColor Cyan

$endTime = Get-Date
$duration = $endTime - $startTime

$summaryReport = @"
HEALTETHER COMPREHENSIVE SECURITY SCAN SUMMARY
==============================================
Scan Date: $($startTime.ToString("yyyy-MM-dd HH:mm:ss"))
Duration: $($duration.ToString("hh\:mm\:ss"))
Scan Type: $ScanType
Timeout per Scan: $TimeoutMinutes minutes

PROJECTS SCANNED:
"@

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $summaryReport += "`n- $($result.Project): $status"
    if (!$result.Success -and $result.Error) {
        $summaryReport += "`n  Error: $($result.Error)"
    }
}

$summaryReport += @"

SECURITY REPORTS GENERATED:
- Clinics API Reports: zap-clinics-$ScanType-$TIMESTAMP.*
- Communications API Reports: zap-communications-$ScanType-$TIMESTAMP.*

REPORT FORMATS:
- JSON: Machine-readable vulnerability data
- HTML: Human-readable vulnerability reports (recommended for review)
- XML: Structured vulnerability data for integration

NEXT STEPS:
1. Open HTML reports for detailed vulnerability analysis
2. Prioritize fixes based on risk levels (High > Medium > Low)
3. Focus on critical security vulnerabilities first
4. Implement security controls for identified issues
5. Re-run scans after fixes to verify remediation
6. Consider running full scans if only baseline was performed

REPORT LOCATIONS:
All reports are saved in the '$RESULTS_DIR' directory.
Open the HTML files in your browser for detailed analysis.
"@

# Save combined summary report
$summaryFile = "$RESULTS_DIR\combined-security-summary-$TIMESTAMP.txt"
$summaryReport | Out-File -FilePath $summaryFile -Encoding UTF8

# Display final results
Write-Host "`n🏁 COMBINED SECURITY SCAN COMPLETED" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host "Duration: $($duration.ToString("hh\:mm\:ss"))" -ForegroundColor Cyan

foreach ($result in $results) {
    $status = if ($result.Success) { "✅ SUCCESS" } else { "❌ FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    Write-Host "$($result.Project): $status" -ForegroundColor $color
    if (!$result.Success -and $result.Error) {
        Write-Host "  Error: $($result.Error)" -ForegroundColor Yellow
    }
}

Write-Host "`n📄 Combined summary: $summaryFile" -ForegroundColor Cyan
Write-Host "📁 All reports in: $RESULTS_DIR" -ForegroundColor Cyan
Write-Host "📊 Open HTML reports for detailed vulnerability analysis" -ForegroundColor Cyan

# Open reports if requested
if ($OpenReports) {
    Write-Host "`n🌐 Opening security reports..." -ForegroundColor Cyan
    
    $clinicsReport = "$RESULTS_DIR\zap-clinics-$ScanType-$TIMESTAMP.html"
    $communicationsReport = "$RESULTS_DIR\zap-communications-$ScanType-$TIMESTAMP.html"
    
    if (Test-Path $clinicsReport) {
        Start-Process $clinicsReport
        Start-Sleep -Seconds 2
    }
    
    if (Test-Path $communicationsReport) {
        Start-Process $communicationsReport
    }
}

# Exit with appropriate code
$failedCount = ($results | Where-Object { !$_.Success }).Count
if ($failedCount -gt 0) {
    Write-Host "`n⚠️  $failedCount project(s) failed security scan" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "`n🎉 All projects scanned successfully!" -ForegroundColor Green
    exit 0
}
