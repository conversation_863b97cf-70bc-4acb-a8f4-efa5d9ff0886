# SonarQube Analysis Script for Healtether Clinics API
# Performs comprehensive code quality analysis using SonarQube Scanner and API client

param(
    [string]$SonarUrl = "http://localhost:9000",
    [switch]$SkipApiReport = $false,
    [switch]$OpenDashboard = $false
)

Write-Host "🔍 SonarQube Analysis - Healtether Clinics API" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Configuration
$PROJECT_KEY = "healtether-clinics-api"
$PROJECT_NAME = "Healtether Clinics API"
$PROJECT_DIR = "Healtether.Clinics"
$SONAR_TOKEN = "sqp_9833dac32acc6233060116e797a16d7c314fcf5c"

# Check if project directory exists
if (!(Test-Path $PROJECT_DIR)) {
    Write-Host "❌ Project directory not found: $PROJECT_DIR" -ForegroundColor Red
    Write-Host "   Please run this script from the web-app root directory" -ForegroundColor Yellow
    exit 1
}

# Check if SonarQube server is accessible
Write-Host "🔗 Checking SonarQube server connectivity..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$SonarUrl/api/system/status" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SonarQube server is accessible at $SonarUrl" -ForegroundColor Green
    }
}
catch {
    Write-Host "❌ SonarQube server is not accessible at $SonarUrl" -ForegroundColor Red
    Write-Host "   Please ensure SonarQube is running or check the URL" -ForegroundColor Yellow
    Write-Host "   Start SonarQube: docker-compose -f docker-compose.sonarqube.yml up -d" -ForegroundColor Yellow
    exit 1
}

# Navigate to project directory
Write-Host "📁 Navigating to project directory: $PROJECT_DIR" -ForegroundColor Cyan
Set-Location $PROJECT_DIR

# Check for sonar-project.properties
if (!(Test-Path "sonar-project.properties")) {
    Write-Host "❌ sonar-project.properties not found in $PROJECT_DIR" -ForegroundColor Red
    Write-Host "   Creating basic configuration..." -ForegroundColor Yellow
    
    $sonarConfig = @"
sonar.projectKey=$PROJECT_KEY
sonar.projectName=$PROJECT_NAME
sonar.projectVersion=1.0
sonar.sources=.
sonar.exclusions=node_modules/**,coverage/**,__tests__/**,*.test.js,*.spec.js
sonar.tests=__tests__
sonar.test.inclusions=**/*.test.js,**/*.spec.js
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.testExecutionReportPaths=coverage/test-reporter.xml
"@
    
    $sonarConfig | Out-File -FilePath "sonar-project.properties" -Encoding UTF8
    Write-Host "✅ Created sonar-project.properties" -ForegroundColor Green
}

# Run tests to generate coverage (if test script exists)
if (Test-Path "package.json") {
    $packageJson = Get-Content "package.json" | ConvertFrom-Json
    if ($packageJson.scripts.test) {
        Write-Host "🧪 Running tests to generate coverage..." -ForegroundColor Cyan
        try {
            npm test
            Write-Host "✅ Tests completed successfully" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠️  Tests failed, but continuing with analysis..." -ForegroundColor Yellow
        }
    }
}

# Run SonarQube Scanner
Write-Host "🔍 Starting SonarQube analysis..." -ForegroundColor Cyan
Write-Host "   Project: $PROJECT_NAME" -ForegroundColor Gray
Write-Host "   Key: $PROJECT_KEY" -ForegroundColor Gray
Write-Host "   Server: $SonarUrl" -ForegroundColor Gray

try {
    # Use sonar-scanner from the parent directory or system PATH
    $scannerPath = "..\sonar-scanner-6.2.1.4610-windows-x64\bin\sonar-scanner.bat"
    if (!(Test-Path $scannerPath)) {
        $scannerPath = "sonar-scanner"  # Try system PATH
    }
    
    $scannerArgs = @(
        "-Dsonar.host.url=$SonarUrl",
        "-Dsonar.login=$SONAR_TOKEN",
        "-Dsonar.projectKey=$PROJECT_KEY",
        "-Dsonar.projectName=$PROJECT_NAME"
    )
    
    & $scannerPath $scannerArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SonarQube analysis completed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ SonarQube analysis failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        Set-Location ..
        exit $LASTEXITCODE
    }
}
catch {
    Write-Host "❌ Failed to run SonarQube scanner" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
    Set-Location ..
    exit 1
}

# Return to root directory
Set-Location ..

# Generate detailed API report
if (!$SkipApiReport) {
    Write-Host "📊 Generating detailed API report..." -ForegroundColor Cyan
    
    if (Test-Path "sonarqube-api-client.js") {
        try {
            node sonarqube-api-client.js $PROJECT_KEY
            Write-Host "✅ Detailed API report generated" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠️  Failed to generate API report, but analysis completed" -ForegroundColor Yellow
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  API client not found, skipping detailed report" -ForegroundColor Yellow
    }
}

# Open SonarQube dashboard
if ($OpenDashboard) {
    Write-Host "🌐 Opening SonarQube dashboard..." -ForegroundColor Cyan
    $dashboardUrl = "$SonarUrl/dashboard?id=$PROJECT_KEY"
    Start-Process $dashboardUrl
}

Write-Host "`n🎉 Analysis Complete!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "📊 View results at: $SonarUrl/dashboard?id=$PROJECT_KEY" -ForegroundColor Cyan
Write-Host "📄 Detailed report: sonar-reports/sonarqube-report-$PROJECT_KEY.json" -ForegroundColor Cyan
Write-Host "`n💡 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Review quality gate status and metrics" -ForegroundColor White
Write-Host "   2. Address critical and major issues first" -ForegroundColor White
Write-Host "   3. Improve test coverage if below threshold" -ForegroundColor White
Write-Host "   4. Re-run analysis after fixes" -ForegroundColor White
