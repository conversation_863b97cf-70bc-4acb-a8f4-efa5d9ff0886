# ZAP Security Testing Script for Healtether Clinics API
# Performs comprehensive vulnerability scanning using OWASP ZAP with Docker

param(
    [string]$ScanType = "baseline",  # Options: baseline, full, api
    [int]$TimeoutMinutes = 30,
    [string]$TargetUrl = "http://host.docker.internal:2222",
    [switch]$SkipHealthCheck = $false,
    [switch]$OpenReport = $false
)

Write-Host "🔒 ZAP Security Testing - Healtether Clinics API" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Configuration
$PROJECT_NAME = "clinics"
$RESULTS_DIR = "zap-results"
$TIMESTAMP = Get-Date -Format "yyyyMMdd-HHmmss"

# Create results directory
if (!(Test-Path $RESULTS_DIR)) {
    New-Item -ItemType Directory -Path $RESULTS_DIR | Out-Null
    Write-Host "✅ Created results directory: $RESULTS_DIR" -ForegroundColor Green
}

# Function to check service health
function Test-ServiceHealth {
    param([string]$Url, [string]$ServiceName)
    
    Write-Host "🔍 Checking $ServiceName health at $Url..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $ServiceName is running and accessible" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️  $ServiceName responded with status: $($response.StatusCode)" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ $ServiceName is not accessible" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "   Please ensure the Clinics API is running on port 2222" -ForegroundColor Yellow
        return $false
    }
}

# Function to check Docker availability
function Test-DockerAvailability {
    Write-Host "🐳 Checking Docker availability..." -ForegroundColor Cyan
    
    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker is available: $dockerVersion" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ Docker is not available" -ForegroundColor Red
        Write-Host "   Please install Docker Desktop and ensure it's running" -ForegroundColor Yellow
        return $false
    }
}

# Function to pull ZAP Docker image
function Get-ZapDockerImage {
    Write-Host "📥 Pulling OWASP ZAP Docker image..." -ForegroundColor Cyan
    
    try {
        docker pull ghcr.io/zaproxy/zaproxy:stable
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ ZAP Docker image ready" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to pull ZAP Docker image" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error pulling ZAP Docker image" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to run ZAP scan
function Start-ZapScan {
    param(
        [string]$Target,
        [string]$ScanMode,
        [int]$Timeout
    )
    
    $outputBase = "$RESULTS_DIR\zap-$PROJECT_NAME-$ScanMode-$TIMESTAMP"
    
    Write-Host "🔍 Starting $ScanMode security scan..." -ForegroundColor Cyan
    Write-Host "   Target: $Target" -ForegroundColor Gray
    Write-Host "   Timeout: $Timeout minutes" -ForegroundColor Gray
    Write-Host "   Output: $outputBase.*" -ForegroundColor Gray
    
    # Prepare Docker command based on scan type
    $dockerArgs = @(
        "run", "--rm",
        "-v", "`"$(pwd)/$RESULTS_DIR`":/zap/wrk/:rw",
        "-t", "ghcr.io/zaproxy/zaproxy:stable"
    )
    
    switch ($ScanMode) {
        "baseline" {
            $zapArgs = @(
                "zap-baseline.py",
                "-t", $Target,
                "-J", "$outputBase.json",
                "-H", "$outputBase.html",
                "-r", "$outputBase.xml"
            )
        }
        "full" {
            $zapArgs = @(
                "zap-full-scan.py",
                "-t", $Target,
                "-J", "$outputBase.json",
                "-H", "$outputBase.html",
                "-r", "$outputBase.xml",
                "-T", $Timeout
            )
        }
        "api" {
            # For API scanning, try to use OpenAPI/Swagger endpoint
            $swaggerUrl = "$Target/api-docs"
            $zapArgs = @(
                "zap-api-scan.py",
                "-t", $swaggerUrl,
                "-f", "openapi",
                "-J", "$outputBase.json",
                "-H", "$outputBase.html",
                "-r", "$outputBase.xml"
            )
        }
        default {
            Write-Host "❌ Unknown scan type: $ScanMode" -ForegroundColor Red
            return $false
        }
    }
    
    $allArgs = $dockerArgs + $zapArgs
    Write-Host "   Command: docker $($allArgs -join ' ')" -ForegroundColor Gray
    
    try {
        & docker $allArgs
        
        # Check if scan completed successfully (ZAP returns 0 for no high-risk issues, 1 for warnings, 2 for high-risk)
        if ($LASTEXITCODE -le 1) {
            Write-Host "✅ $ScanMode scan completed" -ForegroundColor Green
            
            # Verify output files were created
            $files = @("$outputBase.json", "$outputBase.html", "$outputBase.xml")
            foreach ($file in $files) {
                if (Test-Path $file) {
                    Write-Host "   📄 Generated: $file" -ForegroundColor Green
                } else {
                    Write-Host "   ⚠️  Missing: $file" -ForegroundColor Yellow
                }
            }
            
            return $true
        } else {
            Write-Host "⚠️  $ScanMode scan completed with warnings (exit code: $LASTEXITCODE)" -ForegroundColor Yellow
            return $true  # Still consider it successful as ZAP often returns non-zero for findings
        }
    }
    catch {
        Write-Host "❌ Failed to run $ScanMode scan" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Main execution
Write-Host "🚀 Starting security analysis for Healtether Clinics API" -ForegroundColor Cyan
Write-Host "   Scan Type: $ScanType" -ForegroundColor Gray
Write-Host "   Target URL: $TargetUrl" -ForegroundColor Gray
Write-Host "   Timeout: $TimeoutMinutes minutes" -ForegroundColor Gray

# Pre-flight checks
$checksPass = $true

if (!$SkipHealthCheck) {
    if (!(Test-ServiceHealth -Url $TargetUrl -ServiceName "Clinics API")) {
        $checksPass = $false
    }
}

if (!(Test-DockerAvailability)) {
    $checksPass = $false
}

if (!(Get-ZapDockerImage)) {
    $checksPass = $false
}

if (!$checksPass) {
    Write-Host "`n❌ Pre-flight checks failed. Cannot proceed with security scan." -ForegroundColor Red
    exit 1
}

# Run the security scan
Write-Host "`n🎯 Executing security scan..." -ForegroundColor Yellow
$scanSuccess = Start-ZapScan -Target $TargetUrl -ScanMode $ScanType -Timeout $TimeoutMinutes

# Generate summary
$endTime = Get-Date
$summaryFile = "$RESULTS_DIR\clinics-security-summary-$TIMESTAMP.txt"

$summary = @"
HEALTETHER CLINICS API SECURITY SCAN SUMMARY
===========================================
Scan Date: $endTime
Scan Type: $ScanType
Target URL: $TargetUrl
Timeout: $TimeoutMinutes minutes
Status: $(if ($scanSuccess) { "COMPLETED" } else { "FAILED" })

GENERATED REPORTS:
- JSON Report: zap-$PROJECT_NAME-$ScanType-$TIMESTAMP.json
- HTML Report: zap-$PROJECT_NAME-$ScanType-$TIMESTAMP.html  
- XML Report: zap-$PROJECT_NAME-$ScanType-$TIMESTAMP.xml

NEXT STEPS:
1. Open the HTML report for detailed vulnerability analysis
2. Review and prioritize security findings by risk level
3. Implement fixes for identified vulnerabilities
4. Re-run security scan to verify remediation
5. Consider running full scan if only baseline was performed

For detailed analysis, open: $RESULTS_DIR\zap-$PROJECT_NAME-$ScanType-$TIMESTAMP.html
"@

$summary | Out-File -FilePath $summaryFile -Encoding UTF8

# Final results
Write-Host "`n🏁 SECURITY SCAN COMPLETED" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

if ($scanSuccess) {
    Write-Host "✅ Scan completed successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Scan failed" -ForegroundColor Red
}

Write-Host "`n📁 Results saved in: $RESULTS_DIR" -ForegroundColor Cyan
Write-Host "📋 Summary: $summaryFile" -ForegroundColor Cyan
Write-Host "📊 Open HTML report for detailed analysis" -ForegroundColor Cyan

# Open report if requested
if ($OpenReport) {
    $htmlReport = "$RESULTS_DIR\zap-$PROJECT_NAME-$ScanType-$TIMESTAMP.html"
    if (Test-Path $htmlReport) {
        Write-Host "`n🌐 Opening security report..." -ForegroundColor Cyan
        Start-Process $htmlReport
    }
}

exit $(if ($scanSuccess) { 0 } else { 1 })
