{"@programName": "ZAP", "@version": "2.16.1", "@generated": "Thu, 19 Jun 2025 09:04:07", "site": [{"@name": "http://host.docker.internal:3001", "@host": "host.docker.internal", "@port": "3001", "@ssl": "false", "alerts": [{"pluginid": "10049", "alertRef": "10049", "alert": "Non-Storable Content", "name": "Non-Storable Content", "riskcode": "0", "confidence": "2", "riskdesc": "Informational (Medium)", "desc": "<p>The response contents are not storable by caching components such as proxy servers. If the response does not contain sensitive, personal or user-specific information, it may benefit from being stored and cached, to improve performance.</p>", "instances": [{"id": "1", "uri": "http://host.docker.internal:3001/robots.txt", "method": "GET", "param": "", "attack": "", "evidence": "429", "otherinfo": ""}], "count": "1", "solution": "<p>The content may be marked as storable by ensuring that the following conditions are satisfied:</p><p>The request method must be understood by the cache and defined as being cacheable (\"GET\", \"HEAD\", and \"POST\" are currently defined as cacheable)</p><p>The response status code must be understood by the cache (one of the 1XX, 2XX, 3XX, 4XX, or 5XX response classes are generally understood)</p><p>The \"no-store\" cache directive must not appear in the request or response header fields</p><p>For caching by \"shared\" caches such as \"proxy\" caches, the \"private\" response directive must not appear in the response</p><p>For caching by \"shared\" caches such as \"proxy\" caches, the \"Authorization\" header field must not appear in the request, unless the response explicitly allows it (using one of the \"must-revalidate\", \"public\", or \"s-maxage\" Cache-Control response directives)</p><p>In addition to the conditions above, at least one of the following conditions must also be satisfied by the response:</p><p>It must contain an \"Expires\" header field</p><p>It must contain a \"max-age\" response directive</p><p>For \"shared\" caches such as \"proxy\" caches, it must contain a \"s-maxage\" response directive</p><p>It must contain a \"Cache Control Extension\" that allows it to be cached</p><p>It must have a status code that is defined as cacheable by default (200, 203, 204, 206, 300, 301, 404, 405, 410, 414, 501).</p>", "otherinfo": "", "reference": "<p>https://datatracker.ietf.org/doc/html/rfc7234</p><p>https://datatracker.ietf.org/doc/html/rfc7231</p><p>https://www.w3.org/Protocols/rfc2616/rfc2616-sec13.html</p>", "cweid": "524", "wascid": "13", "sourceid": "6"}]}], "sequences": []}